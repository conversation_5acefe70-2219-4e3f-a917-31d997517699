properties([
    disableConcurrentBuilds()
])

pipeline {
    agent any

    environment {
        LANG = 'zh_CN.UTF-8'
        LC_ALL = 'zh_CN.UTF-8'
        FLUTTER_VERSION = '3.24.5'
        FIXED_GIT_URL = 'http://************:8090/flutter/f_wd.git'
        credentialsId = '553e5a24-910c-47fb-8c13-a3045779f662'
        TELEGRAM_CHAT_ID = -4765875551
    }

    parameters {
        gitParameter(
                name: 'GIT_BRANCH',
                type: 'PT_BRANCH',
                defaultValue: 'dev',
                description: '选择要构建的 Git 分支/标签',
                branchFilter: 'origin/(.*)',
                selectedValue: 'DEFAULT',
                sortMode: 'ASCENDING_SMART'
        )
        choice(name: 'platform', choices: ['all', 'android', 'ios', 'web'], description: '构建平台')
        choice(name: 'flavor', choices: ['WD'], description: '选择需打包的项目\nWD：WD')
        choice(name: 'environment', choices: ['release'], description: '构建环境')
        string(name: 'buildVersionName', defaultValue: '', description: '自定义版本号（可选）')
    }


    stages {

        stage('Read ngrok URL') {
            steps {
                script {
                    def filePath = '/usr/local/bin/ngrok_webhook/ngrok_url.txt'
                    if (fileExists(filePath)) {
                        def url = readFile(filePath).trim()
                        echo "读取到 ngrok URL: ${url}"
                        env.NGROK_URL = url
                    } else {
                        error("❌ ngrok URL 文件不存在: ${filePath}")
                    }
                }
            }
        }

        stage('拉取代码') {
            steps {
                git branch: "${params.GIT_BRANCH}",
                        credentialsId: "${env.credentialsId}",
                        url: "${env.FIXED_GIT_URL}"
            }
        }

        stage('init PATH') {
            steps {
                script {
                    env.PATH = "/opt/homebrew/lib/ruby/gems/3.4.0/bin:" +
                            "/opt/homebrew/bin:" +
                            "/Users/<USER>/dev/flutter/bin:" +
                            env.PATH
                }
                sh 'echo PATH=$PATH'
                sh 'which sh'
                sh 'which fvm'
                sh 'which flutter'
            }
        }

        stage('初始化 & 获取版本号') {
            steps {
                script {
                    sh "fvm use ${FLUTTER_VERSION}"

                    env.ACTUAL_FLAVOR = params.flavor
                    env.ACTUAL_ENVIRONMENT = params.environment
                    env.ENVIRONMENT_CAPITALIZED = ACTUAL_ENVIRONMENT.capitalize()
                    env.ACTUAL_DEBUG_VALUE = params.environment == 'debug' ? 'true' : 'false'
                    env.GIT_SHA1 = sh(script: "git rev-parse --short HEAD", returnStdout: true).trim()

                    sh "fvm flutter clean"
                    sh "./tool/assets_swap/assets_swap.sh ${ACTUAL_FLAVOR}"

                    def versionLine = sh(script: "grep 'version:' pubspec.yaml", returnStdout: true).trim()
                    def versionParts = versionLine.replace("version:", "").trim().split("\\+")
                    env.VERSION_NAME = params.buildVersionName?.trim() ?: versionParts[0]
                    env.VERSION_CODE = versionParts.length > 1 ? versionParts[1] : "1"
                }

                echo "🔢 版本: ${VERSION_NAME} + ${VERSION_CODE}"
                echo "🌱 分支: ${params.GIT_BRANCH}, 🎯 环境: ${ACTUAL_ENVIRONMENT}, 🎮 FLAVOR: ${ACTUAL_FLAVOR}"
            }
        }

        stage('构建 Android') {
            when {
                expression { params.platform == 'android' || params.platform == 'all' }
            }
            steps {
                sh """
                  fvm flutter build apk \
                    --flavor ${ACTUAL_FLAVOR} \
                    --dart-define=CHANNEL=${ACTUAL_FLAVOR} \
                    --dart-define=DEBUG=${ACTUAL_DEBUG_VALUE} \
                    --release \
                    --obfuscate \
                    --split-debug-info=xx \
                    --build-name=${VERSION_NAME} \
                    --build-number=${VERSION_CODE} 
                """
            }
        }

        stage('归档 APK') {
            when {
                expression { params.platform == 'android' || params.platform == 'all' }
            }
            steps {
                script {
                    def archiveDir = "${env.HOME}/Desktop/Archive/FWD/${ACTUAL_FLAVOR}/${ACTUAL_ENVIRONMENT}"
                    def oldApkDir = "${archiveDir}/old_apk"
                    def buildApkPath = "build/app/outputs/flutter-apk/app-${ACTUAL_FLAVOR}-release.apk"
                    def newApkName = "${ACTUAL_FLAVOR}_${VERSION_NAME}_b${VERSION_CODE}_${GIT_SHA1}_${ACTUAL_ENVIRONMENT}.apk"

                    sh """
                        mkdir -p "${oldApkDir}" "${archiveDir}"
                        if ls "${archiveDir}"/*.apk 1> /dev/null 2>&1; then
                          mv "${archiveDir}"/*.apk "${oldApkDir}/"
                        fi
                        if [[ -f "${buildApkPath}" ]]; then
                          mv "${buildApkPath}" "${archiveDir}/${newApkName}"
                          echo "✅ APK 文件已归档：${newApkName}"
                        else
                          echo "❌ 未找到 APK 文件"
                          exit 1
                        fi
                    """
                }
            }
        }

        stage('构建 iOS') {
            when {
                expression { params.platform == 'ios' || params.platform == 'all' }
            }
            steps {

                sh """
                  fvm flutter build ios \
                    --flavor ${ACTUAL_FLAVOR} \
                    --dart-define=CHANNEL=${ACTUAL_FLAVOR} \
                    --release \
                    --dart-define=DEBUG=${ACTUAL_DEBUG_VALUE} \
                    --build-name=${VERSION_NAME} \
                    --build-number=${VERSION_CODE} \
                    --no-codesign
                    
                  FLUTTER_BUILD_MODE=release \
                  xcodebuild -workspace ios/Runner.xcworkspace \
                    -scheme ${ACTUAL_FLAVOR} \
                    -sdk iphoneos \
                    -configuration Release-${ACTUAL_FLAVOR} \
                    -archivePath build/Runner.xcarchive \
                    archive 
        
                  xcodebuild -exportArchive \
                    -archivePath build/Runner.xcarchive \
                    -exportPath build/Runner \
                    -exportOptionsPlist ios/Flavor/${ACTUAL_FLAVOR}/exportOptions.plist
                """
            }
        }

        stage('归档 IPA') {
            when {
                expression { params.platform == 'ios' || params.platform == 'all' }
            }
            steps {
                script {
                    def archiveDir = "${env.HOME}/Desktop/Archive/FWD/${ACTUAL_FLAVOR}/${ACTUAL_ENVIRONMENT}"
                    def oldIpaDir = "${archiveDir}/old_ipa"
                    def ipaFile = sh(script: "find build/Runner -name '*.ipa' | head -n 1", returnStdout: true).trim()
                    def newIpaName = "${ACTUAL_FLAVOR}_${VERSION_NAME}_b${VERSION_CODE}_${GIT_SHA1}_${ACTUAL_ENVIRONMENT}.ipa"

                    sh """
                        mkdir -p "${oldIpaDir}" "${archiveDir}"
                        if ls "${archiveDir}"/*.ipa 1> /dev/null 2>&1; then
                          mv "${archiveDir}"/*.ipa "${oldIpaDir}/"
                        fi
                        if [[ -f "${ipaFile}" ]]; then
                          mv "${ipaFile}" "${archiveDir}/${newIpaName}"
                          echo "✅ IPA 文件已归档：${newIpaName}"
                        else
                          echo "❌ 未找到 IPA 文件"
                          exit 1
                        fi
                    """
                }
            }
        }

        stage('构建 Web') {
            when {
                expression { params.platform == 'web' || params.platform == 'all' }
            }
            steps {
                sh """
                    flutter build web \
                    --dart-define=CHANNEL=${flavor} \
                    --dart-define=DEBUG=false \
                    --web-renderer html \
                    --no-web-resources-cdn --release 
                """
            }
        }

        stage('归档 Web') {
            when {
                expression { params.platform == 'web' || params.platform == 'all' }
            }
            steps {
                script {

                    sh """
                      set -e
                    
                      WEB_DIR="build/web"
                      ZIP_PATH="build/web.zip"
                      ARCHIVE_DIR=\"\$HOME/Desktop/Archive/FWD/${ACTUAL_FLAVOR}/${ACTUAL_ENVIRONMENT}\"
                      OLD_WEB_DIR="\$ARCHIVE_DIR/old_web"
                      NEW_WEB_NAME="web.zip"
                      BUILD_WEB_PATH="build/web.zip"
                    
                      echo "当前路径为：\$(pwd)"
                      echo "正在压缩 Web 目录内容..."
                    
                      cd \$WEB_DIR
                      zip -r ../web.zip . > /dev/null 2>&1
                      cd -
                    
                      if [[ ! -f "\$ZIP_PATH" ]]; then
                        echo "错误：压缩失败，未找到 \$ZIP_PATH"
                        exit 1
                      fi
                    
                      echo "Web 目录已成功压缩为 web.zip"
                    
                      if [[ ! -d "\$ARCHIVE_DIR" ]]; then
                        echo "目录 \$ARCHIVE_DIR 不存在，正在创建..."
                        mkdir -p "\$ARCHIVE_DIR"
                      fi
                    
                      OLD_ZIP_FILES=(\$ARCHIVE_DIR/*.zip)
                      if [[ -f "\${OLD_ZIP_FILES[0]}" ]]; then
                        echo "发现旧的 WEB 文件，准备移动到 \$OLD_WEB_DIR..."
                    
                        if [[ ! -d "\$OLD_WEB_DIR" ]]; then
                          echo "目录 \$OLD_WEB_DIR 不存在，正在创建..."
                          mkdir -p "\$OLD_WEB_DIR"
                        fi
                    
                        for file in "\$ARCHIVE_DIR"/*.zip; do
                          if [[ -f "\$file" ]]; then
                            timestamp=\$(date -r "\$file" +"%Y-%m-%d_%H-%M")
                            newName="web_\${timestamp}.zip"
                            echo "正在重命名 \$file 为 \$newName"
                            mv "\$file" "\$OLD_WEB_DIR/\$newName"
                          fi
                        done
                    
                        echo "旧的 WEB 文件已移动并重命名。"
                      else
                        echo "没有发现旧的 WEB 文件。"
                      fi
                    
                      if [[ ! -f "\$BUILD_WEB_PATH" ]]; then
                        echo "错误：未找到 WEB 文件：\$BUILD_WEB_PATH"
                        exit 1
                      fi
                    
                      echo "正在重命名并移动 WEB 文件..."
                      mv "\$BUILD_WEB_PATH" "\$ARCHIVE_DIR/\$NEW_WEB_NAME"
                      echo "WEB 文件已成功移动并命名为 \$NEW_WEB_NAME"
                    """

                }
            }
        }

        stage('内部分发') {
            steps {
                script {
                    def output = sh(
                            script: """
                                set -e
            
                                chmod +x tool/internal_testing_page/*.sh
            
                                export buildVersionName=${params.buildVersionName}
                                export flavor=${params.flavor}
                                export environment=${params.environment}
            
                                tool/internal_testing_page/generate_manifest.sh
                                tool/internal_testing_page/build_internal_distribution.sh
                            """,
                            returnStdout: true
                    ).trim()

                    def urlLine = output.readLines().find { it.contains('[BUILD_OUTPUT_URL]') }
                    env.DOWNLOAD_URL = urlLine?.replace('[BUILD_OUTPUT_URL]', '')?.trim()

                    echo "✅ 下载地址：${env.DOWNLOAD_URL}"
                }
            }
        }

    }

    post {
        always {
            sh "./tool/assets_swap/rollback_assets.sh ${ACTUAL_FLAVOR}"
            httpRequest(
                    url: "${env.NGROK_URL}/jenkins_callback",
                    httpMode: 'POST',
                    contentType: 'APPLICATION_JSON',
                    requestBody: groovy.json.JsonOutput.toJson([
                            project     : params.flavor,
                            result      : currentBuild.currentResult,
                            version     : env.VERSION_NAME,
                            chat_id     : env.TELEGRAM_CHAT_ID,
                            download_url: env.DOWNLOAD_URL
                    ])
            )
        }
        success {
            echo "🎉 构建成功：平台 ${params.platform}，flavor ${params.flavor}"
        }
        failure {
            echo "🚨 构建失败！请检查日志"
        }
    }
}
