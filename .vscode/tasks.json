{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Setup skin: GP",
      "type": "shell",
      "command": "./scripts/switch_flavor.sh --flavor gp --skin gp --color-scheme default",
    },
    {
      "label": "Setup skin: PRE",
      "type": "shell",
      "command": "./scripts/switch_flavor.sh --flavor pre",
    },
    {
      "label": "Setup skin: RSYP",
      "type": "shell",
      "command": "./scripts/switch_flavor.sh --flavor rsyp",
    },
    {
      "label": "Setup skin: YHXT",
      "type": "shell",
      "command": "./scripts/switch_flavor.sh --flavor yhxt",
    },
    {
      "label": "Setup skin: TEMPA",
      "type": "shell",
      "command": "./scripts/switch_flavor.sh --flavor tempa --skin template_a --color-scheme default",
    },
    {
      "label": "Setup skin: TEMPD",
      "type": "shell",
      "command": "./scripts/switch_flavor.sh --flavor tempd --skin template_d --color-scheme default",
    },
    // {
    //   "label": "Switch Flavor: GP",
    //   "type": "shell",
    //   "command": "./scripts/switch_flavor.sh gp",
    //   "presentation": {
    //     "reveal": "always",
    //     "panel": "shared"
    //   },
    //   "problemMatcher": []
    // },
    // {
    //   "label": "Switch Skin: GP default",
    //   "type": "shell",
    //   "command": "./scripts/switch_skin.sh gp default",
    //   "presentation": {
    //     "reveal": "always",
    //     "panel": "shared"
    //   },
    //   "problemMatcher": []
    // },
    // {
    //   "label": "Prelaunch All GP",
    //   "type": "shell",
    //   "dependsOn": [
    //     "Switch Flavor: GP",
    //     "Switch Skin: GP default"
    //   ],
    //   "dependsOrder": "sequence",
    // },
    // {
    //   "label": "Switch Flavor: TempA",
    //   "type": "shell",
    //   "command": "./scripts/switch_flavor.sh tempa",
    //   "presentation": {
    //     "reveal": "always",
    //     "panel": "shared"
    //   },
    //   "problemMatcher": []
    // },
    // {
    //   "label": "Prelaunch All TempA",
    //   "type": "shell",
    //   "dependsOn": [
    //     "Switch Flavor: TempA",
    //     "Switch Skin: template_a default"
    //   ],
    //   "dependsOrder": "sequence",
    // },
    // { 
    //   "label": "Switch Flavor: RSYP",
    //   "type": "shell",
    //   "command": "./scripts/switch_flavor.sh rsyp",
    //   "presentation": {
    //     "reveal": "always",
    //     "panel": "shared"
    //   },
    //   "problemMatcher": []
    // },
    // {
    //   "label": "Switch Skin: template_a default",
    //   "type": "shell",
    //   "command": "./scripts/switch_skin.sh template_a default",
    //   "presentation": {
    //     "reveal": "always",
    //     "panel": "shared"
    //   },
    //   "problemMatcher": []
    // },
    // {
    //   "label": "Switch Flavor: YHXT",
    //   "type": "shell",
    //   "command": "./scripts/switch_flavor.sh yhxt",
    //   "presentation": {
    //     "reveal": "always",
    //     "panel": "shared"
    //   },
    //   "problemMatcher": []
    // },
    {
      "label": "Build APK (GP)",
      "type": "shell",
      "command": "flutter build apk --release --flavor gp -t lib/main_gp.dart",
      "dependsOn": "Switch Flavor: GP",
      "group": "build",
      "presentation": {
        "reveal": "always",
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Build APK (RSYP)",
      "type": "shell",
      "command": "flutter build apk --release --flavor rsyp -t lib/main_rsyp.dart",
      "dependsOn": "Switch Flavor: RSYP",
      "group": "build",
      "presentation": {
        "reveal": "always",
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Build IPA (GP)",
      "type": "shell",
      "command": "flutter build ipa --release --flavor gp -t lib/main_gp.dart",
      "dependsOn": "Switch Flavor: GP",
      "group": "build",
      "presentation": {
        "reveal": "always",
        "panel": "shared"
      },
      "problemMatcher": [],
      "options": {
        "shell": {
          "executable": "/bin/bash",
          "args": [
            "-c"
          ]
        }
      }
    },
    {
      "label": "Build IPA (RSYP)",
      "type": "shell",
      "command": "flutter build ipa --release --flavor rsyp -t lib/main_rsyp.dart",
      "dependsOn": "Switch Flavor: RSYP",
      "group": "build",
      "presentation": {
        "reveal": "always",
        "panel": "shared"
      },
      "problemMatcher": [],
      "options": {
        "shell": {
          "executable": "/bin/bash",
          "args": [
            "-c"
          ]
        }
      }
    },
    {
      "label": "Setup Git Hooks",
      "type": "shell",
      "command": "./scripts/setup-git-hooks.sh",
      "presentation": {
        "reveal": "always",
        "panel": "shared"
      },
      "problemMatcher": [],
      "group": {
        "kind": "none",
        "isDefault": true
      }
    },
    {
      "label": "Run Code Fix & Format",
      "type": "shell",
      "command": "dart fix --apply . && flutter format .",
      "presentation": {
        "reveal": "always",
        "panel": "shared"
      },
      "problemMatcher": [],
      "group": {
        "kind": "none",
        "isDefault": false
      }
    },
    {
      "label": "Run Flutter Analyze",
      "type": "shell",
      "command": "flutter analyze",
      "presentation": {
        "reveal": "always",
        "panel": "shared"
      },
      "problemMatcher": [],
      "group": {
        "kind": "test",
        "isDefault": true
      }
    }
  ]
}