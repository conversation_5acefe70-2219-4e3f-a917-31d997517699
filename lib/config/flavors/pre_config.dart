import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';

void setupPreConfig() {
  AppConfig(
    flavor: Flavor.pre,
    appName: 'PRE',
    siteId: "1",
    skinStyle: AppSkinStyle.kGP,
    colorSchemeStyle: ColorSchemeStyle.kDefault,
    environment: "pre",
    baseUrl: 'https://abcdefg.gphome.club',
    marketWsUrl: 'wss://abcdefg.gphome.club',
    inviteLinkUrl: 'https://abcdefg.gphome.club/#/?inviteCode=',
    currentTradingModel: TradingMode.stockAndFutures,
    // AES encryption key for GP flavor
    encryptionKey: '8JUOEEGjDsmrl30P',
    wangYiCaptchaKey: '7650f145f0824ba6973d99d43a99d15c',
    ossUrls: [
      "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/pre/1/app_api.json",
      "https://bj-1337543130.cos.ap-beijing.myqcloud.com/pre/1/app_api.json",
      "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/pre/1/app_api.json",
      "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/pre/1/app_api.json",
      "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/pre/1/app_api.json",
    ],
  );
}
