import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';

void setupTempDConfig() {
  AppConfig(
    flavor: Flavor.tempd,
    appName: 'TempD',
    siteId: "1",
    skinStyle: AppSkinStyle.kTemplateD,
    colorSchemeStyle: ColorSchemeStyle.kDefault,
    environment: "test",
    baseUrl: 'https://h5.gpnow.xyz',
    marketWsUrl: 'wss://h5.gpnow.xyz',
    inviteLinkUrl: 'https://h5.gpnow.xyz/#/?inviteCode=',
    currentTradingModel: TradingMode.stockAndFutures,
    // AES encryption key for GP flavor
    encryptionKey: '8JUOEEGjDsmrl30P',
    wangYiCaptchaKey: '7650f145f0824ba6973d99d43a99d15c',
    ossUrls: [
      "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/test/1/app_api.json",
      "https://bj-1337543130.cos.ap-beijing.myqcloud.com/test/1/app_api.json",
      "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/test/1/app_api.json",
      "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/test/1/app_api.json",
      "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/test/1/app_api.json",
    ],
  );
}
