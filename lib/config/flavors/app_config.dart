import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';

/// Defines the flavor of the app
enum Flavor {
  pre, // 预发布环境
  gp, // 测试环境
  rsyp, // 荣顺优配 (生产-演示站)
  yhxt, // 沅和信投 (生产-租户站)
  tempa, // 金盾 (生产-租户站)
  tempd, // 金币 (生产-租户站)
}

/// Configuration for the app based on flavor
class AppConfig {
  final Flavor flavor;
  final String appName;
  final String siteId;
  final AppSkinStyle skinStyle;
  final ColorSchemeStyle colorSchemeStyle;
  final String environment;
  final String baseUrl;
  final String marketWsUrl;
  final String inviteLinkUrl;
  final TradingMode tradingMode;
  final String encryptionKey;
  final String wangYiCaptchaKey;
  final List<String> ossUrls;

  static AppConfig? _instance;

  factory AppConfig({
    required Flavor flavor,
    required String appName,
    required String siteId,
    required AppSkinStyle skinStyle,
    required ColorSchemeStyle colorSchemeStyle,
    required String environment,
    required String baseUrl,
    required String marketWsUrl,
    required String inviteLinkUrl,
    required TradingMode currentTradingModel,
    required String encryptionKey,
    required String wangYiCaptchaKey,
    required List<String> ossUrls,
  }) {
    _instance ??= AppConfig._internal(
      flavor: flavor,
      appName: appName,
      siteId: siteId,
      skinStyle: skinStyle,
      colorSchemeStyle: colorSchemeStyle,
      environment: environment,
      baseUrl: baseUrl,
      marketWsUrl: marketWsUrl,
      inviteLinkUrl: inviteLinkUrl,
      tradingMode: currentTradingModel,
      encryptionKey: encryptionKey,
      wangYiCaptchaKey: wangYiCaptchaKey,
      ossUrls: ossUrls,
    );
    return _instance!;
  }

  AppConfig._internal({
    required this.flavor,
    required this.appName,
    required this.siteId,
    required this.skinStyle,
    required this.colorSchemeStyle,
    required this.environment,
    required this.baseUrl,
    required this.marketWsUrl,
    required this.inviteLinkUrl,
    required this.tradingMode,
    required this.encryptionKey,
    required this.wangYiCaptchaKey,
    required this.ossUrls,
  });

  static AppConfig get instance {
    assert(_instance != null, 'AppConfig has not been initialized');
    return _instance!;
  }

  bool get isDev => flavor == Flavor.gp;

  String get flavorName => flavor.name;
  String get flavorTitle => flavor.name.toUpperCase();
}
