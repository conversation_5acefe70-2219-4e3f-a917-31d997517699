import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/shared/models/exchange_rate/exchange_rate.dart';
import 'package:gp_stock_app/shared/models/sys_settings_model/sys_config_model.dart';
import 'package:gp_stock_app/shared/models/sys_settings_model/sys_settings_model.dart';
import 'package:injectable/injectable.dart';

@singleton
class CommonServices {
  Future<ResponseResult<List<ExchangeRate>>> getExchangeRate() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getRateInfo,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> rateList = response.data['data'] ?? [];
          final List<ExchangeRate> exchangeRates = rateList.map((json) => ExchangeRate.fromJson(json)).toList();

          return ResponseResult(data: exchangeRates);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get exchange rate');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Unknown error occurred');
    }
  }

  Future<ResponseResult<SysSettingsModel>> getSysSettings() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.systemSettings,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Parse the entire response
        final Map<String, dynamic> responseData = response.data;

        if (responseData['code'] == 0) {
          // Extract the data field and parse it as SysSettingsModel
          final SysSettingsModel sysSettings = SysSettingsModel.fromJson(responseData['data'] ?? {});
          return ResponseResult(data: sysSettings);
        } else {
          return ResponseResult(error: responseData['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get system settings');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Unknown error occurred');
    }
  }

  static Future<SysConfigModel?> getSysConfig() async {
    final response = await Http().request<SysConfigModel>(
      ApiEndpoints.systemConfig,
      method: HttpMethod.get,
    );
    return response.data;
  }
}
