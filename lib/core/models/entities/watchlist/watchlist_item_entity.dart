import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/watchlist_item_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/watchlist_item_entity.g.dart';

@JsonSerializable()
class WatchlistItemEntity {
	String createTime = '';
	double gain = 0;
	int id = 0;
	String instrument = '';
	double latestPrice = 0;
	String market = '';
	String name = '';
	int putSort = 0;
	String securityType = '';
	int sort = 0;
	String symbol = '';
	String updateTime = '';
	int userId = 0;

	WatchlistItemEntity();

	factory WatchlistItemEntity.fromJson(Map<String, dynamic> json) => $WatchlistItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $WatchlistItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}