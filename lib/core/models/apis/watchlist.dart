import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

/// 自选接口
class WatchlistApi {
  /// 通过Instrument获取自选Entity
  static Future<WatchlistItemEntity?> getWatchlistByInstrument(String instrument) async {
    final res = await Http().request<WatchlistItemEntity>(
      ApiEndpoints.getWatchListBySymbol,
      method: HttpMethod.get,
      queryParameters: {
        "market": instrument.split('|')[0],
        "securityType": instrument.split('|')[1],
        "symbol": instrument.split('|')[2],
      },
    );
    /// data为空的情况下，id等于默认值0，既没有自选
    return res.data?.id != 0 ? res.data : null;
  }


}
