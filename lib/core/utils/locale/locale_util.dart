import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/locale_entity.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/shared/widgets/common_bottom_sheet.dart';
import 'package:wd/shared/widgets/common_button.dart';

const kAppLocale = "kAppLocale";

class LocaleUtil {
  static final LocaleUtil _instance = LocaleUtil._internal();
  factory LocaleUtil() => _instance;
  LocaleUtil._internal();

  Locale _appLocale = const Locale('en', 'US');
  Locale get appLocale => _appLocale;
  String appLocaleLanguageTag = ""; // app语言-用于请求headers.content-language
  late List<LocaleEntity> supportLocaleList;

  set appLocale(Locale locale) {

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = sl<NavigatorService>().navigatorKey.currentState!.context;
      context.setLocale(locale);
    });

    _appLocale = locale;
    appLocaleLanguageTag = appLocale.toLanguageTag().replaceAll("-", "_");
    saveLocale(locale);
  }

  /// 初始化语言设置
  Future<void> init() async {
    supportLocaleList = getSupportLocaleList();
    appLocale = await setupLocale();
  }

  /// 设置接口返回的用户语言
  setupRemoteLocale(String remoteLocaleTag) {
    final parts = remoteLocaleTag.split("_");
    final remoteLocale = Locale(parts[0], parts.length > 1 ? parts[1] : null);
    if (remoteLocale != appLocale) {
      // 获取支持的语言列表
      final supportLocales = supportLocaleList.map((e) => e.toLocale()).toSet();
      if (supportLocales.contains(remoteLocale)) {
        appLocale = remoteLocale;
      }
    }
  }

  /// 设置应用语言
  /// 
  /// 语言选择优先级：
  /// 1. 用户保存的语言设置（如果存在且在支持列表中）
  /// 2. 系统语言（如果在支持列表中）
  /// 3. 服务器配置的默认语言（如果在支持列表中）
  /// 4. 中文（作为最后的兜底选项）
  Future<Locale> setupLocale() async {
    // 获取支持的语言列表
    final supportLocales = supportLocaleList.map((e) => e.toLocale()).toSet();

    // 1. 尝试使用用户保存的语言设置
    final savedLocale = await loadSavedLocale();
    if (savedLocale != null && supportLocales.contains(savedLocale)) {
      return savedLocale;
    }

    // 2. 尝试使用系统语言
    final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
    if (supportLocales.contains(systemLocale)) {
      return systemLocale;
    }

    // 3. 尝试使用服务器配置的默认语言
    final defaultLocaleTag = GlobalConfig().systemConfig.languageType.defaultLanguage;
    final parts = defaultLocaleTag.split("_");
    final defaultLocale = Locale(parts[0], parts.length > 1 ? parts[1] : null);
    if (supportLocales.contains(defaultLocale)) {
      return defaultLocale;
    }

    // 4. 兜底
    return const Locale('en', 'US') ;
  }

  /// 获取支持的语言列表
  List<LocaleEntity> getSupportLocaleList() {
    try {
      // 获取API支持的语言列表
      final apiSupportList = GlobalConfig().systemConfig.languageType.list.map((e) {
        final parts = e.dictValue.split("_");
        return Locale(parts[0], parts.length > 1 ? parts[1] : null);
      }).toSet();

      // 直接从kLocaleList中筛选出API支持的语言
      return kLocaleList.where((entity) {
        final locale = entity.toLocale();
        return apiSupportList.contains(locale);
      }).toList();
    } catch (e) {
      // 发生错误时返回默认语言列表
      return kLocaleList;
    }
  }

  /// 加载保存的语言设置
  Future<Locale?> loadSavedLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final tag = prefs.getString(kAppLocale);
    if (tag != null) {
      final parts = tag.split('-');
      final locale = Locale(parts[0], parts.length > 1 ? parts[1] : null);
      return locale;
    }
    return null;
  }

  /// 保存语言设置
  Future<void> saveLocale(Locale locale) async {
    await SharedPreferences.getInstance().then((prefs) {
      final languageTag = locale.toLanguageTag();
      prefs.setString(kAppLocale, languageTag);
      /// 告知服务器用户修改了语言
      if (sl.isRegistered<UserCubit>()) {
        UserApi.updateLanguage(languageTag.replaceAll("-", "_"));
      }
    });
  }
}
