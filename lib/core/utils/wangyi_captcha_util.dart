import 'dart:ui';

// ignore: depend_on_referenced_packages
import 'package:captcha_plugin_flutter/captcha_plugin_flutter.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';

typedef StringCallBack = void Function(String str);

class WangYiCaptchaUtil {
  late CaptchaPluginFlutter captchaPlugin;
  bool isShowing = false;

  WangYiCaptchaUtil() {
    captchaPlugin = CaptchaPluginFlutter();
  }

  void show({
    required String account,
    required StringCallBack onSuccess,
    VoidCallback? onValidateFailClose,
    VoidCallback? onError,
  }) {
    if (isShowing) return;

    isShowing = true;
    bool isSuccess = false;

    captchaPlugin.showCaptcha(
      onLoaded: () {},
      onSuccess: (dynamic data) {
        isShowing = false;
        if (data is! Map) return;
        var validate = data["validate"];
        if (validate == null || isEmpty(validate)) {
          return GPEasyLoading.showToast(data["message"]);
        }
        isSuccess = true;
        onSuccess(validate);
      },
      onClose: (dynamic data) {
        isShowing = false;
        if (!isSuccess) {
          onValidateFailClose?.call();
        }
      },
      onError: (dynamic data) {
        isShowing = false;
        onError?.call();
      },
    );
  }
}
