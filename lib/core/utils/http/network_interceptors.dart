import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/locale/locale_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'aes_cipher.dart';
import 'https.dart';
import 'network_extension.dart';

class CommonInterceptors extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _appendCommonHeaders(options.headers);
    _appendCommonData(options);
    if (options.enableAesEncrypt) {
      options.data = {'param': AESCipher().encryptObject(options.data)};
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.data == null) {
      throw DioException(
        requestOptions: response.requestOptions,
        error: NetException.dataNotFound.message,
        type: DioExceptionType.badResponse,
      );
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    GSEasyLoading.dismiss();
    if (!kIgnoreApiList.contains(err.requestOptions.path)) {
      String errorMsg = _getErrorMessage(err);
      GSEasyLoading.showToast(errorMsg);
    }
    super.onError(err, handler);
  }

  /// 往header添加动态公参
  void _appendCommonHeaders(Map<String, dynamic> headers) {
    if (sl.isRegistered<UserCubit>()) {
      final userState = sl<UserCubit>().state;
      if (userState.isLogin) {
        headers['Authorization'] = 'Bearer ${userState.loginInfo?.token}';
      }
    }
    if (LocaleUtil().appLocaleLanguageTag.isNotEmpty) {
      headers['content-language'] = LocaleUtil().appLocaleLanguageTag;
    }
  }

  /// 往header添加动态公参
  void _appendCommonData(RequestOptions options) {
// 构建公共参数
    final Map<String, dynamic> commonParams = {
      "requestTime": DateTime.now().millisecondsSinceEpoch.toString(),
    };

    final method = options.method.toUpperCase();
    if (method == 'GET') {
      // 🔹 合并 GET 请求的 query 参数
      options.queryParameters =
          Map<String, dynamic>.from(options.queryParameters)
            ..addAll(commonParams);
    } else if (['POST', 'PUT', 'PATCH'].contains(method)) {
      // 🔹 POST / PUT / PATCH 请求
      if (options.data is Map<String, dynamic>) {
        options.data = Map<String, dynamic>.from(options.data)
          ..addAll(commonParams);
      } else if (options.data is FormData) {
        final formData = options.data as FormData;
        for (final entry in commonParams.entries) {
          formData.fields.add(MapEntry(entry.key, entry.value.toString()));
        }
      }
    }
  }

  /// 获取错误消息
  String _getErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请检查网络';
      case DioExceptionType.sendTimeout:
        return '请求超时，请稍后再试';
      case DioExceptionType.receiveTimeout:
        return '服务器响应超时，请稍后再试';
      case DioExceptionType.badCertificate:
        return '证书验证失败，请检查网络安全设置';
      case DioExceptionType.badResponse:
        return _handleDioBadResponse(error.response);
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接错误，请检查网络设置';
      case DioExceptionType.unknown:
        return '发生未知错误，请稍后重试';
    }
  }

  String _handleDioBadResponse(Response? response) {
    if (response == null) return '服务器无响应，请稍后再试';

    switch (response.statusCode) {
      case 400:
        return '请求参数错误';
      case 401:
        return '未授权，请重新登录';
      case 403:
        return '没有权限访问此资源';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误，请稍后再试';
      default:
        return '请求失败，状态码：${response.statusCode}';
    }
  }
}

class LogsInterceptors extends InterceptorsWrapper {
  final List<String> ignoredUrls;

  LogsInterceptors({this.ignoredUrls = const []});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (!_shouldIgnore(options)) {
      LogD("REQUEST[${options.method}] => PATH: ${options.path}");
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (!_shouldIgnore(response.requestOptions)) {
      String? json;
      // ignore: unnecessary_null_comparison
      if (response != null) {
        json = fmt(response.data, 1);
        json = 'Return Data: $json';
      } else {
        json = 'response 不存在';
      }
      dynamic params;
      if (response.requestOptions.method == 'GET') {
        params = response.requestOptions.queryParameters;
      } else {
        params = response.requestOptions.data;
      }
      LogD(
          "✅ ${response.requestOptions.baseUrl} \n✅ ${response.requestOptions.path} \n✅ METHOD:${response.requestOptions.method} \n✅ HEADER:${fmt(response.requestOptions.headers, 1)} \n✅ Body:${fmt(params, 1)}\n$json");
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (!_shouldIgnore(err.requestOptions)) {
      dynamic params;
      if (err.requestOptions.method == 'GET') {
        params = err.requestOptions.queryParameters;
      } else {
        params = err.requestOptions.data;
      }
      LogD("❌ ${err.requestOptions.baseUrl} \n"
          "❌ ${err.requestOptions.path} \n"
          "❌ METHOD:${err.requestOptions.method} \n"
          "❌ HEADER:${fmt(err.requestOptions.headers, 1)} \n"
          "❌ Body:${fmt(params, 1)}\n"
          "❌ Error: ${err.error}\n"
          "❌ Error Type: ${err.type}\n"
          "❌ Error Message: ${err.message}\n"
          "❌ Stack Trace: ${err.stackTrace}");
    }

    super.onError(err, handler);
  }

  bool _shouldIgnore(RequestOptions options) {
    for (var url in ignoredUrls) {
      if (options.path.contains(url)) {
        return true;
      }
    }
    return false;
  }
}

String fmt(dynamic json, int indent) {
  const encoder = JsonEncoder.withIndent('  ');
  return encoder.convert(json);
}
