import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/bank_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_picker.dart';
import 'package:wd/shared/widgets/sheet/fund_pwd_sheet.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'add_wallet_state.dart';

class AddWalletCubit extends Cubit<AddWalletState> {
  AddWalletCubit() : super(AddWalletState().init());

  bool _isFetching = false;

  fetchBankListData() async {
    if (_isFetching) return;

    _isFetching = true;
    GSEasyLoading.showLoading();
    final list = await UserApi.fetchBankList(WithdrawType.wallet);
    GSEasyLoading.dismiss();
    _isFetching = false;

    state.dataList = list;
    emit(state.clone());
  }

  int retryCount = 0;
  int lastIndex = 0;

  void onClickShowBankListPicker({required BuildContext context, required TextEditingController controller}) async {
    sl<NavigatorService>().unFocus();

    if (_isFetching) return;

    if (state.dataList!.isEmpty) {
      if (retryCount == 3) return;
      retryCount += 1;
      await Future.delayed(const Duration(milliseconds: 500));
      await fetchBankListData();
      if (context.mounted) {
        onClickShowBankListPicker(context: context, controller: controller); // 重新调用时检查 mounted
      }
      return;
    }

    CommonPicker(
      dataList: (state.dataList as List<BankEntity>).map((e) => e.bankName).toList(),
      index: lastIndex,
      callback: (index) {
        lastIndex = index;
        state.selectBank = state.dataList![index];
        controller.text = state.selectBank!.bankName;
        emit(state.clone());
      },
    ).show(context);
  }

  void onClickSwitch(bool value) {
    state.isDefaultCard = value;
    emit(state.clone());
  }

  void onSubmit(context, {required String cardNo}) async {
    sl<NavigatorService>().unFocus();
    if (state.formKey.currentState!.validate()) {
      if (sl<UserCubit>().isFundPasswordInputLocked) {
        GSEasyLoading.showToast("支付密码连续错误5次，请1分钟后重试");
        return;
      }
      final fundPwd = await FundPwdSheet(context).show();
      if (fundPwd == null) return;

      try {
        GSEasyLoading.showLoading();
        final flag = await UserApi.addBankCard(
            type: WithdrawType.wallet,
            bankCode: state.selectBank!.bankCode,
            bankName: state.selectBank!.bankName,
            cardNo: cardNo,
            realName: sl<UserCubit>().state.userInfo?.realName ?? "",
            fundPwd: fundPwd);
        if (sl<UserCubit>().state.userInfo!.realName.isEmpty) {
          await sl<UserCubit>().fetchUserInfo();
        }
        GSEasyLoading.dismiss();
        if (flag) {
          sl<UserCubit>().resetFundPasswordErrorLock();
          GSEasyLoading.showToast("添加成功");
          await Future.delayed(const Duration(milliseconds: 500));
          sl<NavigatorService>().pop();
        }
      } catch (e) {
        GSEasyLoading.dismiss();
        GSEasyLoading.showToast("添加失败，请重试");
      }
    }
  }
}
