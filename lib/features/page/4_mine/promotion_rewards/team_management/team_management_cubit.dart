import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/team_details_entity.dart';
import '../../../../../core/models/entities/team_members_entity.dart';
import '../../../../../shared/widgets/easy_loading.dart';

part 'team_management_state.dart';

enum TeamManagementTab { member, bet, profit }

class TeamManagementCubit extends Cubit<TeamManagementState> {
  TeamManagementCubit() : super(const TeamManagementState());

  void updatePageNo(int pageNo) {
    if (isClosed) return;
    emit(state.copyWith(pageNo: pageNo));
  }

  void updatePageNoToNext() {
    emit(state.copyWith(pageNo: state.pageNo + 1));
  }

  void updateTab(TeamManagementTab tab) {
    emit(state.copyWith(tab: tab));
  }

  void resetTeamData() {
    emit(state.copyWith(
        resetTeamDetailsEntity: true,
        teamDetailsNetState: NetState.initializeState,
        resetTeamMembersEntity: true,
        teamMembersNetState: NetState.initializeState));
  }

  Future<void> fetchTeamMemberInfo({String? childUserId}) async {
    GSEasyLoading.showLoading();
    try {
      final teamMembersEntity = await PromotionApi.fetchTeamMemberInfo(
        pageNo: state.pageNo,
        childUserId: childUserId,
      );
      GSEasyLoading.dismiss();

      if (teamMembersEntity == null) {
        emit(state.copyWith(
          teamMembersNetState: NetState.error404State,
        ));
      } else if (teamMembersEntity.records?.isEmpty == true) {
        emit(state.copyWith(
          teamMembersNetState: NetState.emptyDataState,
        ));
      } else {
        if (state.pageNo == 1) {
          emit(state.copyWith(
            teamMembersEntity: teamMembersEntity,
            teamMembersNetState:
                teamMembersEntity.records?.isNotEmpty == true ? NetState.dataSuccessState : NetState.emptyDataState,
          ));
        } else {
          final currentRecords = state.teamMembersEntity?.records ?? [];
          final newRecords = teamMembersEntity.records ?? [];

          teamMembersEntity.records = [...currentRecords, ...newRecords];

          emit(state.copyWith(
            teamMembersEntity: teamMembersEntity,
            teamMembersNetState:
                teamMembersEntity.records?.isNotEmpty == true ? NetState.dataSuccessState : NetState.emptyDataState,
          ));
        }
        emit(state.copyWith(
          isNoMoreData: (teamMembersEntity.total ?? 0) <= (teamMembersEntity.records?.length ?? 0),
        ));
      }
    } on Error catch (e) {
      GSEasyLoading.dismiss();
      emit(state.copyWith(
        teamMembersNetState: NetState.errorShowRefresh,
        error: e.toString(),
      ));
    }
  }

  Future<void> fetchMyTeamDetail({String? childUserId, required TeamType type}) async {
    GSEasyLoading.showLoading();
    try {
      final teamDetailsEntity = await PromotionApi.fetchMyTeamDetail(
        pageNo: state.pageNo,
        childUserId: childUserId,
        type: type,
      );
      GSEasyLoading.dismiss();

      if (teamDetailsEntity == null) {
        emit(state.copyWith(
          teamDetailsNetState: NetState.error404State,
        ));
      } else if (teamDetailsEntity.records?.isEmpty == true) {
        emit(state.copyWith(
          teamDetailsNetState: NetState.emptyDataState,
        ));
      } else {
        if (state.pageNo == 1) {
          emit(state.copyWith(
            teamDetailsEntity: teamDetailsEntity,
            teamDetailsNetState:
                teamDetailsEntity.records?.isNotEmpty == true ? NetState.dataSuccessState : NetState.emptyDataState,
          ));
        } else {
          final currentRecords = state.teamDetailsEntity?.records ?? [];
          final newRecords = teamDetailsEntity.records ?? [];

          teamDetailsEntity.records = [...currentRecords, ...newRecords];

          emit(state.copyWith(
            teamDetailsEntity: teamDetailsEntity,
            teamDetailsNetState:
                teamDetailsEntity.records?.isNotEmpty == true ? NetState.dataSuccessState : NetState.emptyDataState,
          ));
        }
        emit(state.copyWith(
          isNoMoreData: (teamDetailsEntity.total ?? 0) <= (teamDetailsEntity.records?.length ?? 0),
        ));
      }
    } on Error catch (e) {
      GSEasyLoading.dismiss();
      emit(state.copyWith(
        teamDetailsNetState: NetState.errorShowRefresh,
        error: e.toString(),
      ));
    }
  }

  Future<bool> loadMoreTeamMemberInfo() async {
    if (isClosed) return false;
    if (state.isNoMoreData) return false;
    emit(state.copyWith(pageNo: state.pageNo + 1));
    await fetchTeamMemberInfo();
    return !state.isNoMoreData;
  }

  Future<bool> loadMoreMyTeamDetail({required TeamType type}) async {
    if (isClosed) return false;
    if (state.isNoMoreData) return false;
    emit(state.copyWith(pageNo: state.pageNo + 1));
    await fetchMyTeamDetail(type: type);
    return !state.isNoMoreData;
  }
}
