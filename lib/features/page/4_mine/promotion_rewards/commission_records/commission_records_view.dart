// commission_records_view.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import '../../../../../core/base/common_refresher.dart';
import '../../../../../core/constants/assets.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_details_entity.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/common_tabbar.dart';
import '../../../../../shared/widgets/common_table.dart';
import '../../../../../core/theme/themes.dart';
import '../../../../routers/navigator_utils.dart';
import 'commission_records_cubit.dart';

// View
class CommissionRecordsView extends StatelessWidget {
  const CommissionRecordsView({super.key});

  @override
  Widget build(BuildContext context) => BlocProvider(
        create: (_) => CommissionRecordsCubit()..fetchCommissionRecords(),
        child: const _CommissionRecordsContent(),
      );
}

class _CommissionRecordsContent extends StatefulWidget {
  const _CommissionRecordsContent();

  @override
  State<_CommissionRecordsContent> createState() => _CommissionRecordsContentState();
}

class _CommissionRecordsContentState extends State<_CommissionRecordsContent> with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: DayType.values.length, vsync: this)..addListener(_onTabChanged);
  }

  void _onTabChanged() {
    context.read<CommissionRecordsCubit>().resetCommissionDetailsEntity();
    if (_tabController.indexIsChanging) return;
    if (_tabController.index != _tabController.previousIndex) {
      context.read<CommissionRecordsCubit>().changeDateType(_tabController.index);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      );

  PreferredSizeWidget _buildAppBar() => AppBar(
        leading: InkWell(
          onTap: () {
            sl<NavigatorService>().unFocus();
            sl<NavigatorService>().pop();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 14.gw),
            child: Image(
              image: const AssetImage(Assets.iconBack),
              height: 20.gw,
              width: 20.gw,
            ),
          ),
        ),
        centerTitle: true,
        title: BlocSelector<CommissionRecordsCubit, CommissionRecordsState, TeamType>(
          selector: (state) => state.paymentTabIndex,
          builder: (context, state) => CommonTabBar.withAutoKey(
            [
              CommonTabBarItem(
                title: 'betting'.tr(),
                imageUrl: Assets.iconPromotionBet,
              ),
              CommonTabBarItem(title: 'recharge'.tr(), imageUrl: Assets.iconPromotionRecharge),
            ],
            currentIndex: state.index,
            onTap: (index) => context.read<CommissionRecordsCubit>().changePaymentTabIndex(index),
            isScrollable: false,
          ),
        ),
        actions: [
          BlocSelector<CommissionRecordsCubit, CommissionRecordsState, DayType>(
            selector: (state) => state.dateType,
            builder: (context, dateType) => _buildDateTypeDropdown(context, dateType),
          ),
          SizedBox(width: 16.gw),
        ],
      );

  Widget _buildBody() => Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 12.gw,
        ),
        child: TabBarView(
          controller: _tabController,
          children: List.generate(
            3,
            (_) => CommissionRecordsContent(),
          ),
        ),
      );

  Widget _buildDateTypeDropdown(BuildContext context, DayType selectedType) {
    return PopupMenuButton<DayType>(
      onSelected: (DayType type) {
        _tabController.animateTo(type.index);
      },
      offset: const Offset(0, 35),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.gw),
      ),
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<DayType>(
          value: DayType.yesterday,
          child: Row(
            children: [
              Text(
                'yesterday'.tr(),
                style:
                    selectedType == DayType.yesterday ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
        PopupMenuItem<DayType>(
          value: DayType.thisMonth,
          child: Row(
            children: [
              Text(
                'this_month'.tr(),
                style:
                    selectedType == DayType.thisMonth ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
        PopupMenuItem<DayType>(
          value: DayType.allTime,
          child: Row(
            children: [
              Text(
                'all_time'.tr(),
                style: selectedType == DayType.allTime ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
      ],
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.borderA,
          borderRadius: BorderRadius.circular(10.gw),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getDateTypeLabel(selectedType),
              style: context.textTheme.title.fs13.w500,
            ),
            SizedBox(width: 4.gw),
            Icon(
              Icons.arrow_drop_down,
              size: 20.gw,
              color: context.colorTheme.textTitle,
            ),
          ],
        ),
      ),
    );
  }

  String _getDateTypeLabel(DayType type) {
    switch (type) {
      case DayType.yesterday:
        return 'yesterday'.tr();
      case DayType.thisMonth:
        return 'this_month'.tr();
      case DayType.allTime:
        return 'all_time'.tr();
    }
  }
}

// Content Widget
class CommissionRecordsContent extends StatelessWidget {
  CommissionRecordsContent({super.key});
  final TextEditingController _controller = TextEditingController();
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  Widget build(BuildContext context) => BlocBuilder<CommissionRecordsCubit, CommissionRecordsState>(
        builder: (context, state) => Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            if (state.commissionDetailsNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text(
                    'no_data_found'.tr(),
                    style: context.textTheme.secondary,
                  ),
                ),
              ),
            if (state.commissionDetailsNetState == NetState.dataSuccessState)
              Expanded(
                child: _CommissionTable(
                  records: state.commissionDetailsEntity?.records ?? [],
                  paymentType: state.paymentTabIndex,
                  refreshController: refreshController,
                ),
              ),
          ],
        ),
      );

  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 24.gw,
                  color: context.colorTheme.textHighlight,
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: context.textTheme.highlight,
                    decoration: InputDecoration(
                      fillColor: context.theme.cardColor,
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: context.textTheme.highlight,
                      filled: true,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.btnBgPrimary,
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: context.colorTheme.btnBorderPrimary,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () =>
                  context.read<CommissionRecordsCubit>().fetchCommissionRecords(childUserId: _controller.text.trim()),
              child: Text(
                'search'.tr(),
                style: context.textTheme.btnPrimary.fs13.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Commission Table
class _CommissionTable extends StatelessWidget {
  final List<CommissionDetailsRecords> records;
  final TeamType paymentType;
  final RefreshController refreshController;

  const _CommissionTable({
    required this.records,
    required this.paymentType,
    required this.refreshController,
  });

  void _onRefresh(BuildContext context) {
    context.read<CommissionRecordsCubit>().updatePageNo(1);
    context.read<CommissionRecordsCubit>().fetchCommissionRecords();
    refreshController.resetNoData();
    refreshController.refreshCompleted();
  }

  void _onLoading(BuildContext context) async {
    final hasMore = await context.read<CommissionRecordsCubit>().loadMoreCommissionRecords();
    if (hasMore) {
      refreshController.loadComplete();
    } else {
      refreshController.loadNoData();
    }
  }

  List<CommonTableColumn> _getColumns() {
    if (paymentType == TeamType.bet) {
      return [
        CommonTableColumn(
          title: 'sub_agent'.tr(),
          key: 'subUserNo',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'bet_amount'.tr(),
          key: 'amount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'bet_comm'.tr(),
          key: 'commissionAmount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'date'.tr(),
          key: 'belongDate',
          flex: 2,
          style: CommonTableColumnStyle.yellowText,
        ),
      ];
    } else {
      return [
        CommonTableColumn(
          title: 'sub_agent'.tr(),
          key: 'subUserNo',
          flex: 3,
        ),
        CommonTableColumn(
          title: 'recharge_amount'.tr(),
          key: 'amount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'recharge_commission_amount'.tr(),
          key: 'commissionAmount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'date'.tr(),
          key: 'belongDate',
          flex: 3,
          style: CommonTableColumnStyle.yellowText,
        ),
      ];
    }
  }

  List<List<String>> _getTableData() {
    return records
        .map((record) => [
              record.subUserNo?.maskString ?? '',
              record.amount?.formattedMoney ?? '',
              record.commissionAmount?.formattedMoney ?? '',
              _formatDate(record.belongDate),
            ])
        .toList();
  }

  String _formatDate(String? date) {
    if (date == null) return '';
    try {
      return DateTime.parse(date).toIso8601String().split('T').first;
    } catch (e) {
      return date;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: CommonRefresher(
        onRefresh: () => _onRefresh(context),
        onLoading: () => _onLoading(context),
        refreshController: refreshController,
        enablePullDown: true,
        enablePullUp: true,
        listWidget: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: CommonTable(
              columns: _getColumns(),
              data: _getTableData(),
            ),
          ),
        ),
      ),
    );
  }
}
