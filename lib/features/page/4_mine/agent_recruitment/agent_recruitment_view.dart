import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/utils/system_util.dart';
import '../../../../shared/widgets/common_button.dart';
import 'agent_recruitment_cubit.dart';
import 'agent_recruitment_state.dart';

/// 代理招募
class AgentRecruitmentView extends BasePage {
  const AgentRecruitmentView({super.key});

  @override
  BasePageState<BasePage> getState() => _AgentRecruitmentViewState();
}

class _AgentRecruitmentViewState extends BasePageState<AgentRecruitmentView> {
  @override
  void initState() {
    pageTitle = "agent_recruitment".tr();
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) => SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.gw),
          child: Column(
            children: [
              // Banner section
              // _buildBanner(),
              // SizedBox(height: 16.gw),
              _buildSubBanner(),
              SizedBox(height: 16.gw),
              // Service cards
              AnimationLimiter(
                child: Column(
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 600),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: widget,
                      ),
                    ),
                    children: [
                      // Online service cards
                      _buildService(),
                      SizedBox(height: 14.gw),
                      // Feature cards
                      _buildFeature(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  Widget _buildService() => BlocBuilder<AgentRecruitmentCubit, AgentRecruitmentState>(
        builder: (context, state) {
          if (state.netState == NetState.loadingState) {
            return _buildShimmerLoading();
          }
          if (state.netState == NetState.dataSuccessState) {
            final data = state.data;
            if (data == null || data.isEmpty) {
              return const EmptyWidget(title: '暂无数据');
            }
            return HeaderContentCard(
              header: AneText(
                'contact_support3'.tr(),
                style: context.textTheme.btnSecondary.fs16.w600,
              ),
              margin: EdgeInsets.zero,
              content: Padding(
                padding: EdgeInsets.all(16.gw),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: data.length,
                  separatorBuilder: (context, index) => SizedBox(height: 8.gw),
                  itemBuilder: (context, index) {
                    final model = data[index];
                    return _buildServiceCard(
                      imageUrl: model.logo,
                      title: model.name,
                      subtitle: model.description,
                      onTap: () {
                        if (model.type == 1) {
                          // 內跳
                          SystemUtil.openCommonWebView(title: model.name, url: model.link);
                        } else if (model.type == 2) {
                          // 外跳
                          SystemUtil.openUrlOnSystemBrowser(url: model.link, needOpenNewTag: true);
                        }
                      },
                    );
                  },
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        },
      );

  // 2x2 grid
  Widget _buildFeature() => Column(
        children: [
          IntrinsicHeight(
            child: Row(
              children: [
                _buildFeatureCard(
                  image: Assets.iconProfite,
                  title: "agent_premium_commission_title".tr(),
                  subtitle: "agent_premium_commission_subtitle".tr(),
                ),
                SizedBox(width: 12.gw),
                _buildFeatureCard(
                  image: Assets.iconEntertainment,
                  title: "agent_advanced_gaming_title".tr(),
                  subtitle: "agent_advanced_gaming_subtitle".tr(),
                ),
              ],
            ),
          ),
          SizedBox(height: 8.gw),
          IntrinsicHeight(
            child: Row(
              children: [
                _buildFeatureCard(
                  image: Assets.iconChannel,
                  title: "universal_device_compatibility".tr(),
                  subtitle: "universal_device_compatibility_subtitle".tr(),
                ),
                SizedBox(width: 12.gw),
                _buildFeatureCard(
                  image: Assets.iconSafe,
                  title: "agent_safe_stable_title".tr(),
                  subtitle: "agent_safe_stable_subtitle".tr(),
                ),
              ],
            ),
          ),
        ],
      );

  Widget _buildBanner() {
    return Stack(
      alignment: Alignment.center,
      children: [
        AppImage(
          imageUrl: Assets.recruitmentTitle,
        ),
        Column(
          children: [
            SizedBox(
              width: 200.gw,
              child: Text.rich(
                TextSpan(
                  text: '${'unite_for'.tr()} ',
                  style: context.textTheme.secondary.fs22.w800.ffAne,
                  children: [
                    TextSpan(
                      text: 'success_achieve'.tr(),
                      style: context.textTheme.primary.fs22.w800.ffAne,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              width: 300.gw,
              child: AneText(
                'agent_recruitment_desc'.tr(),
                textAlign: TextAlign.center,
                style: context.textTheme.title,
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _buildSubBanner() {
    return Stack(
      alignment: Alignment.centerLeft,
      children: [
        AppImage(
          imageUrl: Assets.recruitmentSubTitle,
        ),
        Padding(
          padding: EdgeInsets.only(left: 16.gw),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 200.gw,
                child: AneText(
                  'join_forces'.tr(),
                  style: context.textTheme.primary.fs22.w800.ffAne,
                ),
              ),
              SizedBox(
                width: 200.gw,
                child: AneText(
                  'contact_support2'.tr(),
                  style: context.textTheme.title,
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildServiceCard({
    required String imageUrl,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) =>
      Container(
        decoration: BoxDecoration(
          color: context.theme.dividerColor,
          borderRadius: BorderRadius.circular(8.gw),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 12.gw),
          child: Row(
            children: [
              AppImage(
                radius: 6,
                imageUrl: imageUrl,
                width: 40.gw,
                height: 40.gw,
              ),
              SizedBox(width: 12.gw),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AneText(
                      title,
                      style: context.textTheme.secondary.fs15.w600,
                    ),
                    SizedBox(height: 2.gw),
                    AneText(
                      subtitle,
                      style: context.textTheme.title,
                    ),
                  ],
                ),
              ),
              _buildConsultButton(onTap: onTap),
            ],
          ),
        ),
      );

  Widget _buildFeatureCard({
    required String image,
    required String title,
    required String subtitle,
  }) =>
      Expanded(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.gw),
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1E170B),
                const Color(0xFF161616).withOpacity(0.41),
                const Color(0xFF161616).withOpacity(0.7),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 74,
                offset: const Offset(0, -16),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(12.gw),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 12.gh),
                Container(
                  width: 45.gw,
                  height: 45.gw,
                  padding: EdgeInsets.all(12.gh),
                  decoration: BoxDecoration(
                    color: context.theme.primaryColor,
                    borderRadius: BorderRadius.circular(6.gw),
                  ),
                  child: SvgPicture.asset(
                    image,
                    width: 24.gw,
                    height: 24.gh,
                  ),
                ),
                SizedBox(height: 12.gh),
                Text(
                  title,
                  style: context.textTheme.secondary.fs16.w600.ffAne,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 4.gw),
                Text(
                  subtitle,
                  style: context.textTheme.title.ffAne,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );

  Widget _buildConsultButton({
    required VoidCallback onTap,
  }) =>
      CommonButton(
        title: 'consult_now'.tr(),
        height: 36.gw,
        width: 87.gw,
        fontSize: 14.fs,
        fontWeight: FontWeight.w400,
        radius: 8,
        onPressed: onTap,
      );

  Widget _buildShimmerLoading() => Column(
        children: List.generate(3, (index) => _buildShimmerCard()),
      );

  Widget _buildShimmerCard() => Container(
        margin: EdgeInsets.only(bottom: 8.gw),
        padding: EdgeInsets.all(16.gw),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.gw),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 24.gw,
              height: 24.gw,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.gw),
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.grey[300]!,
                    Colors.grey[100]!,
                    Colors.grey[300]!,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: -2.0, end: 2.0),
                duration: const Duration(milliseconds: 1500),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: Offset(value * 100, 0),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            Colors.white.withOpacity(0.0),
                            Colors.white.withOpacity(0.5),
                            Colors.white.withOpacity(0.0),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                  );
                },
                onEnd: () {
                  setState(() {});
                },
              ),
            ),
            SizedBox(width: 12.gw),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 120.gw,
                    height: 16.gw,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.gw),
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.grey[300]!,
                          Colors.grey[100]!,
                          Colors.grey[300]!,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: -2.0, end: 2.0),
                      duration: const Duration(milliseconds: 1500),
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(value * 100, 0),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colors.white.withOpacity(0.0),
                                  Colors.white.withOpacity(0.5),
                                  Colors.white.withOpacity(0.0),
                                ],
                                stops: const [0.0, 0.5, 1.0],
                              ),
                            ),
                          ),
                        );
                      },
                      onEnd: () {
                        setState(() {});
                      },
                    ),
                  ),
                  SizedBox(height: 8.gw),
                  Container(
                    width: 200.gw,
                    height: 12.gw,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.gw),
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.grey[300]!,
                          Colors.grey[100]!,
                          Colors.grey[300]!,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: -2.0, end: 2.0),
                      duration: const Duration(milliseconds: 1500),
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(value * 100, 0),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colors.white.withOpacity(0.0),
                                  Colors.white.withOpacity(0.5),
                                  Colors.white.withOpacity(0.0),
                                ],
                                stops: const [0.0, 0.5, 1.0],
                              ),
                            ),
                          ),
                        );
                      },
                      onEnd: () {
                        setState(() {});
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
}
