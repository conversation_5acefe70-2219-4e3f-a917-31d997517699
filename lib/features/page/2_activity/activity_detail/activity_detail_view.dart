import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/auth/phone_prefix.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/core/theme/themes.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/constants/constants.dart';
import '../../../../shared/widgets/common_textfield.dart';
import '../../../../shared/widgets/verification_code/verification_code.dart';
import 'activity_detail_cubit.dart';
import 'activity_detail_state.dart';

class ActivityDetailPage extends BasePage {
  final ActivityRecords model;

  const ActivityDetailPage({super.key, required this.model});

  @override
  BasePageState<BasePage> getState() => _ActivityDetailPageState();
}

class _ActivityDetailPageState extends BasePageState<ActivityDetailPage> {
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController verificationCodeController =
      TextEditingController();

  @override
  void initState() {
    pageTitle = 'act_event_details'.tr();

    /// recordType: 0:通知, 1:彩金, 2:申请
    /// activeStatus: 0:未生效, 1:已经生效, 2:已经过期
    if ((widget.model.recordType == 1 || widget.model.recordType == 2) &&
        widget.model.activeStatus == 1) {
      context.read<ActivityDetailCubit>().fetchCollectStatus(widget.model.id);
    }
    super.initState();
  }

  bool _shouldShowButton(ActivityDetailState state) {
    switch (widget.model.recordType) {
      case 0: // Notice
        return false; // No button for notices
      case 1: // Bonus
      case 2: // Application
        return true;
      default:
        return false;
    }
  }

  bool _isButtonEnabled(ActivityDetailState state) {
    if (sl<UserCubit>().state.userInfo?.phoneStatus == 0 &&
        widget.model.actTypeKey == 9) return false;
    if (widget.model.activeStatus != 1) return false;

    switch (widget.model.recordType) {
      case 1: // Bonus
      case 2: // Application
        return state.collectStatus == ActivityRewardStatus.notReceived;
      default:
        return false;
    }
  }

  String _getButtonTitle(ActivityDetailState state) {
    // For expired activities
    if (widget.model.activeStatus == 2) return 'act_expired'.tr();
    // For not yet active activities
    if (widget.model.activeStatus == 0) return 'act_not_started'.tr();

    switch (widget.model.recordType) {
      case 1: // Bonus
        switch (state.collectStatus) {
          case ActivityRewardStatus.notReceived:
            return 'act_claim_now'.tr();
          case ActivityRewardStatus.received:
            return 'act_claimed'.tr();
          case ActivityRewardStatus.expired:
            return 'act_expired'.tr();
          case ActivityRewardStatus.unknown:
          default:
            return '';
        }
      case 2: // Application
        switch (state.collectStatus) {
          case ActivityRewardStatus.notReceived:
            return 'act_apply'.tr();
          case ActivityRewardStatus.received:
            return 'act_applied'.tr();
          case ActivityRewardStatus.expired:
            return 'act_expired'.tr();
          case ActivityRewardStatus.unknown:
          default:
            return '';
        }
      default:
        return '';
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<ActivityDetailCubit, ActivityDetailState>(
      builder: (context, state) {
        final showButton = _shouldShowButton(state);
        final buttonTitle = _getButtonTitle(state);
        final buttonEnabled = _isButtonEnabled(state);

        return CommonCardPage(
          paddingTop: 0,
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(10.0.gw),
              child: Column(
                children: [
                  if (widget.model.actTypeKey == 9) ...[
                    BlocSelector<UserCubit, UserState, bool>(
                        selector: (state) => state.userInfo?.phoneStatus == 0,
                        builder: (context, isUnbindPhone) {
                          return isUnbindPhone
                              ? _phoneVerification(context, state)
                              : _successCard();
                        }),
                  ],
                  SizedBox(height: 10.gw),
                  AppImage(
                    imageUrl: widget.model.detailImage,
                    width: GSScreenUtil().screenWidth,
                    fit: BoxFit.fill,
                    radius: 10.gw,
                  ),
                  if (showButton) ...[
                    SizedBox(height: 10.gw),
                    InkWell(
                      onTap: () {
                        context.read<ActivityDetailCubit>().executeCollect(
                              context,
                              widget.model.id,
                              widget.model.recordType,
                            );
                      },
                      child: buttonEnabled
                          ? normalBtnBG(buttonTitle)
                          : emptyBtnBG(buttonTitle),
                    ),
                    SizedBox(height: 20.gw),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget normalBtnBG(String title) {
    return Container(
      height: 42.gw,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.gw),
      decoration: ShapeDecoration(
        color: context.colorTheme.btnBgPrimary,
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            width: 1,
            color: Color(0xFFFFE157),
          ),
          borderRadius: BorderRadius.circular(8.gw),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              color: const Color(0xFF030303),
              fontSize: 16.fs,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget emptyBtnBG(String title) {
    return Container(
      height: 42.gw,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.gw),
      decoration: ShapeDecoration(
        color: const Color(0xFF6E5502),
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            width: 1,
            color: Color(0xFFC09404),
          ),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: context.textTheme.primary
          ),
        ],
      ),
    );
  }

  Widget _phoneVerification(BuildContext context, ActivityDetailState state) {
    return Container(
      width: GSScreenUtil().screenWidth,
      padding: EdgeInsets.all(14.gw),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
          ),
        ],
      ),
      child: Column(
        children: [
          _buildPhoneInput(context),
          SizedBox(height: 12.gw),
          _buildVerificationCodeInput(context),
          SizedBox(height: 12.gw),
          BlocListener<ActivityDetailCubit, ActivityDetailState>(
            listenWhen: (previous, current) {
              return current.bindStatus != previous.bindStatus;
            },
            listener: (context, state) {
              if (state.bindStatus == SimplyNetStatus.success) {
                sl<UserCubit>().fetchUserInfo();
                context
                    .read<ActivityDetailCubit>()
                    .fetchCollectStatus(widget.model.id);
              }
            },
            child: CommonButton(
              title: 'act_submit'.tr(),
              bgImgPath: 'assets/images/button/bg_button_golden.png',
              height: 51.gw,
              fontSize: 16.fs,
              enable: true,
              onPressed: () => context.read<ActivityDetailCubit>().verifyCode(
                  phoneNo: phoneController.text,
                  code: verificationCodeController.text),
            ),
          ),
        ],
      ),
    );
  }

  Widget _successCard() {
    return Container(
      width: GSScreenUtil().screenWidth,
      padding: EdgeInsets.all(14.gw),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(height: 12.gw),
          Text('act_verified'.tr(),
              style: TextStyle(fontSize: 18.fs, fontWeight: FontWeight.w600)),
          SizedBox(height: 15.gw),
          Text('act_claim_bonus'.tr(),
              style: TextStyle(fontSize: 16.fs, fontWeight: FontWeight.w400)),
          SizedBox(height: 12.gw),
        ],
      ),
    );
  }

  /// Builds the phone input field with country code
  Widget _buildPhoneInput(BuildContext context) {
    return CommonTextField(
      controller: phoneController,
      hintText: "hint_enter_phone".tr(),
      keyboardType: TextInputType.phone,
      maxLength: 11,
      suffixIconPadding: EdgeInsets.zero,
      prefixIconPadding: EdgeInsets.zero,
      prefixIcon: const PhonePrefix(showIcon: true),
      onChanged: (value) =>
          context.read<ActivityDetailCubit>().setPhoneNo(value),
    );
  }

  /// Builds the verification code input field with request button
  Widget _buildVerificationCodeInput(BuildContext context) {
    return CommonTextField(
      controller: verificationCodeController,
      hintText: "hint_enter_verification_code".tr(),
      keyboardType: TextInputType.number,
      prefixIcon: Container(
        padding: EdgeInsets.only(left: 10.gw, right: 14.gw),
        child: Image.asset("assets/images/login/shield.png",
            color: const Color(0xffEACA9F), width: 16.gw, height: 16.gw),
      ),
      suffixIcon: BlocBuilder<ActivityDetailCubit, ActivityDetailState>(
        builder: (context, state) => VerificationCode(
          phone: state.phoneNo,
          isGradient: true,
          onSmsCode: (smsCode) {
            if (kDebug && smsCode.isNotEmpty) {
              verificationCodeController.text = smsCode;
            }
          },
        ),
      ),
      suffixIconPadding: EdgeInsets.zero,
      prefixIconPadding: EdgeInsets.zero,
      onSubmitted: (value) => {},
    );
  }
}
