import 'package:flutter/material.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/2_activity/activity/activity_sign_in_widget.dart';
import 'package:wd/shared/widgets/balance/currency_symbol_icon.dart';
import 'package:wd/shared/widgets/common_bottom_sheet.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/common_copy_button.dart';
import 'package:wd/shared/widgets/common_progress_bar.dart';
import 'package:wd/shared/widgets/common_switch.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';
import 'package:wd/shared/widgets/game/popular/game_home_popular_list_view.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/text_field/game_search_text_field.dart';

import '../../../shared/widgets/common_table.dart';

class DebugUikitPage extends BasePage {
  const DebugUikitPage({super.key});

  @override
  BasePageState<BasePage> getState() => _DebugUikitPageState();
}

class _DebugUikitPageState extends BasePageState {
  @override
  void initState() {
    pageTitle = "UIKit";
    pageBgColor = Colors.white;
    isBack = false;
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildAvatar(),
          _buildCommonButton(style: CommonButtonStyle.primary),
          _buildCommonButton(style: CommonButtonStyle.secondary, radius: 25),
          _buildCommonButton(style: CommonButtonStyle.tertiary),
          _buildCommonButton(style: CommonButtonStyle.quaternary),
          _buildRotatedLineBtn(),
          _buildCommonTabBar(),
          _buildCommonTabBar(style: CommonTabBarStyle.secondary),
          _buildCommonTabBar(style: CommonTabBarStyle.secondary, showEndMark: true),
          _buildCommonTabBar(showIcon: false),
          _buildGameSearchTextField(),
          _buildCommonCard(),
          _buildIconTextfield(),
          _buildHeaderContentCard(context),
          _buildCustomBottomSheet(),
          _buildCommonProgressBar(),
          _buildCurrencySymbolIcon(),
          _buildCommonSwitch(),
          _buildCommonCopyButton(),
          _buildBasicTable(context),
          _buildTableWithYellowLastColumn(context),
          _buildTableWithLevelBadges(context),
        ],
      ),
    );
  }

  Widget _buildSection({required String title, String? subTitle, required Widget child}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 2.gw),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Text(title, style: context.textTheme.highlight.w600),
                if (subTitle != null)
                  Text(
                    subTitle,
                    style: context.textTheme.title.fs12,
                  ),
              ],
            ),
          ),
          SizedBox(width: 15.gw),
          Expanded(flex: 7, child: child)
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return _buildSection(
      title: "Avatar",
      child: Row(
        children: [
          AuthUtil.getAvatarWidget(context, avatarStr: "70", size: Size(48.gw, 48.gw)),
        ],
      ),
    );
  }

  Widget _buildCommonButton({CommonButtonStyle style = CommonButtonStyle.primary, double? radius}) {
    return _buildSection(
        title: "CommonButton",
        subTitle: '${style.name} ${radius != null ? 'r:$radius' : ''}',
        child: CommonButton(
          title: "Login",
          style: style,
          radius: radius,
        ));
  }

  Widget _buildRotatedLineBtn({CommonButtonStyle style = CommonButtonStyle.primary, double? radius}) {
    return _buildSection(
        title: "RotatedLineBtn",
        subTitle: '${style.name} ${radius != null ? 'r:$radius' : ''}',
        child: RotatedLineBtn(
          height: 45.gw,
          content: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Text('abc', style: context.textTheme.title),
              Text("def", style: context.textTheme.primary.fs16.w500),
            ],
          ),
        ));
  }

  Widget _buildCommonTabBar(
      {bool showIcon = true, CommonTabBarStyle style = CommonTabBarStyle.primary, bool showEndMark = false}) {
    return _buildSection(
      title: "CommonTabBar",
      subTitle: "${style.name} ${showIcon ? '' : '.noIcon'} ${showEndMark ? '.mark' : ''}",
      child: CommonTabBar.withAutoKey(
        GameHomePopularType.values
            .map((e) => CommonTabBarItem(title: e.tabTitle, imageUrl: showIcon ? e.tabImagePath : null))
            .toList(),
        style: style,
        showEndMark: showEndMark,
      ),
    );
  }

  Widget _buildGameSearchTextField() {
    return _buildSection(
        title: "GameSearchTextField",
        child: GameSearchTextField(hintText: "hintText", onTapSearch: () {}, onChanged: (t) {}));
  }

  Widget _buildCommonCard() {
    return _buildSection(title: "CommonCard", child: const CommonCard(child: Text("CommonCard")));
  }

  Widget _buildIconTextfield() {
    return _buildSection(
        title: "IconTextfield",
        child: IconTextfield(
          controller: TextEditingController(),
          hintText: "hintText",
          prefixIcon: IconButton(
            icon: const Icon(Icons.remove_red_eye_outlined),
            color: context.colorTheme.textTitle,
            onPressed: () {},
          ),
        ));
  }

  Widget _buildHeaderContentCard(BuildContext context) {
    return _buildSection(
      title: "HeaderContentCard",
      child: HeaderContentCard(
        header: Text("Header", style: context.textTheme.primary.fs16),
        content: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text("Content", style: context.textTheme.regular),
        ),
      ),
    );
  }

  Widget _buildCustomBottomSheet() {
    return _buildSection(
      title: "CommonBottomSheet",
      child: CommonButton(
        title: "CommonBottomSheet",
        onPressed: () {
          CommonBottomSheet.show(
            context: context,
            maxHeight: 431.gw,
            isScrollControlled: false,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 18.gw, vertical: 16.gw),
            title: 'CommonBottomSheet',
            onTapSure: () => debugPrint("onTapSure"),
            children: [
              const Text("content"),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCommonProgressBar() {
    return _buildSection(
      title: "CommonProgressBar",
      child: const CommonProgressBar(total: 10, current: 8),
    );
  }

  Widget _buildCurrencySymbolIcon() {
    return _buildSection(
        title: "CurrencySymbolIcon",
        subTitle: "*Style .primary .secondary",
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CurrencySymbolIcon(),
            SizedBox(width: 10.gw),
            CurrencySymbolIcon(style: CurrencySymbolIconStyle.secondary),
          ],
        ));
  }

  Widget _buildCommonSwitch() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 2.gw),
      child: Row(
        children: [
          Text("CommonSwitch", style: context.textTheme.highlight.w600),
          SizedBox(width: 40.gw),
          CommonSwitch(value: true, onChanged: (value) {}),
        ],
      ),
    );
  }

  Widget _buildCommonCopyButton() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 2.gw),
      child: Row(
        children: [
          Text("CommonCopyButton", style: context.textTheme.highlight.w600),
          SizedBox(width: 40.gw),
          const CommonCopyButton(text: "Copy"),
        ],
      ),
    );
  }

  static Widget _buildBasicTable(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 2.gw),
      child: Column(
        children: [
          Text("BasicTable", style: context.textTheme.highlight.w600),
          const CommonTable(
            columns: [
              CommonTableColumn(title: 'Sub-agent', key: 'agent'),
              CommonTableColumn(title: 'Bet Amount', key: 'amount'),
              CommonTableColumn(title: 'Bet Comm', key: 'commission'),
              CommonTableColumn(title: 'Date', key: 'date'),
            ],
            data: [
              ['test***1', '9999.99', '9999.99', '2024-12-24'],
              ['test***2', '8999.99', '8999.99', '2024-12-24'],
              ['test***3', '7999.99', '7999.99', '2024-12-24'],
            ],
          ),
        ],
      ),
    );
  }

  /// Example 2: Table with yellow last column (like commission records)
  static Widget _buildTableWithYellowLastColumn(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 2.gw),
      child: Column(
        children: [
          Text("TableWithYellowLastColumn", style: context.textTheme.highlight.w600),
          const CommonTable(
            columns: [
              CommonTableColumn(
                title: 'Sub-agent',
                key: 'agent',
              ),
              CommonTableColumn(title: 'Bet Amount', key: 'amount'),
              CommonTableColumn(title: 'Bet Comm', key: 'commission'),
              CommonTableColumn(
                title: 'Date',
                key: 'date',
                style: CommonTableColumnStyle.yellowText, // Yellow text
              ),
            ],
            data: [
              ['test***1', '9999.99', '9999.99', '2024-12-24'],
              ['test***2', '8999.99', '8999.99', '2024-12-24'],
              ['test***3', '7999.99', '7999.99', '2024-12-24'],
            ],
          ),
        ],
      ),
    );
  }

  /// Example 3: Table with level badges (like team hierarchy)
  static Widget _buildTableWithLevelBadges(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 2.gw),
      child: Column(
        children: [
          Text("TableWithLevelBadges", style: context.textTheme.highlight.w600),
          const CommonTable(
            columns: [
              CommonTableColumn(
                title: 'Sub-agent(999)',
                key: 'agent',
              ),
              CommonTableColumn(
                title: 'Level',
                key: 'level',
                style: CommonTableColumnStyle.levelBadge, // Badge style
              ),
              CommonTableColumn(
                title: 'Join Time',
                key: 'joinTime',
              ),
              CommonTableColumn(
                title: 'Date',
                key: 'date',
                style: CommonTableColumnStyle.yellowText, // Yellow text
              ),
            ],
            data: [
              ['test***1', 'Level 1', '18:09:45', '2024-12-24'],
              ['test***2', 'Level 2', '18:09:45', '2024-12-24'],
              ['test***3', 'Level 3', '18:09:45', '2024-12-24'],
            ],
          ),
        ],
      ),
    );
  }
}
