import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/login_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/constants/enums.dart';
import '../../../core/models/country.dart';
import '../../../core/services/country_service.dart';
import '../../../core/services/device_fingerprint_service.dart';
import '../../../shared/widgets/wangyi_captcha/wangyi_captcha.dart';
import 'login_state.dart';
import 'package:wd/core/utils/string_util.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit()
      : super(LoginState(
          username: '',
          phone: '',
          password: '',
          smsCode: '',
          rememberMe: false,
          acceptPrivacyPolicy: !kEnablePrivacyPolicySwitch,
          isPasswordVisible: false,
          loginStatus: SimplyNetStatus.idle,
          usernameController: TextEditingController(),
          phoneController: TextEditingController(),
          passwordController: TextEditingController(),
          verificationCodeController: TextEditingController(),
          smsCodeController: TextEditingController(),
          captchaController: TextEditingController(),
        )) {
    initConfig();
    fetchCaptchaType();
    _loadSavedCredentials();
  }

  /// 初始化登录配置
  Future<void> initConfig() async {
    var systemConfig = GlobalConfig().systemConfig;

    final loginMethod = AuthMethodType.fromConfig(systemConfig.loadLoginAndRegWay.login);
    final initialType = systemConfig.loadLoginAndRegWay.defLogin == '1' ? LoginType.phone : LoginType.userName;

    // Initialize default country
    final defaultCountry = await CountryService.instance.getDefaultCountry();

    emit(state.copyWith(
      authMethodType: loginMethod,
      loginType: initialType,
      selectedCountry: defaultCountry,
    ));
  }

  /// 获取验证类型
  fetchCaptchaType() async {
    final loginCaptchaType = await GlobalConfig().getConfigValueByKey("login_captcha");
    if (loginCaptchaType != null) {
      final captchaType = CaptchaType.values.firstWhere(
        (type) {
          return type.index.toString() == loginCaptchaType;
        },
        orElse: () => CaptchaType.wangYi,
      );
      emit(state.copyWith(captchaType: captchaType));
    }
    if (state.captchaType == CaptchaType.picture) {
      fetchImageCaptcha();
    }
  }

  void _loadSavedCredentials() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final username = prefs.getString('login_username') ?? '';
    final password = prefs.getString('login_password') ?? '';
    final rememberMe = prefs.getBool('login_rememberMe') ?? false;

    state.usernameController.text = username;
    state.passwordController.text = password;

    emit(state.copyWith(
      username: username,
      password: password,
      rememberMe: rememberMe,
    ));
  }

  void usernameChanged(String username) {
    emit(state.copyWith(username: username));
  }

  void phoneChanged(String phone) {
    emit(state.copyWith(phone: phone));
  }

  void smsCodeChanged(String smsCode) {
    emit(state.copyWith(smsCode: smsCode));
  }

  void passwordChanged(String password) {
    emit(state.copyWith(password: password));
  }

  void setVerificationCode(String verificationCode) {
    emit(state.copyWith(verificationCode: verificationCode));
  }

  void toggleRememberMe(bool rememberMe) {
    emit(state.copyWith(rememberMe: rememberMe));
  }

  void toggleAcceptPrivacyPolicy(bool acceptPrivacyPolicy) {
    emit(state.copyWith(acceptPrivacyPolicy: acceptPrivacyPolicy));
  }

  void togglePasswordVisibility() {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  void _showWangYiCaptcha({
    required String account,
    required Function(String result) onSuccess,
  }) {
    WangYiCaptcha().show(
      account: account,
      captchaId: kWangYiVerityKey,
      onSuccess: onSuccess,
      onValidateFailClose: () {
        if (!isClosed) {
          emit(state.copyWith(loginStatus: SimplyNetStatus.idle));
        }
      },
      onError: () {
        if (!isClosed) {
          GSEasyLoading.showToast('verification_failed'.tr());
          emit(state.copyWith(loginStatus: SimplyNetStatus.failed));
        }
      },
    );
  }

  /// 点击登录
  void login(LoginType loginType) async {
    sl<NavigatorService>().unFocus();
    if (loginType == LoginType.phone) {
      if (state.phone.isEmpty) {
        GSEasyLoading.showToast('please_enter_phone'.tr());
        return;
      }
      // Validate phone number length and format
      final bool isValidLength = state.phone.length == 11;
      final bool isValidFormat = RegExp(r'^1[3-9]\d{9}$').hasMatch(state.phone);

      if (!isValidLength || !isValidFormat) {
        GSEasyLoading.showToast('please_enter_correct_phone'.tr());
        return;
      }
      if (state.smsCode.isEmpty) {
        GSEasyLoading.showToast('please_enter_sms_code'.tr());
        return;
      }
    } else if (loginType == LoginType.userName) {
      if (state.username.isEmpty) {
        GSEasyLoading.showToast('please_enter_username_or_email'.tr());
        return;
      }
      // Backend will handle username validation
      // if (!StringUtil.isValidUsername(state.username)) {
      //   GSEasyLoading.showToast('username_invalid_rule'.tr());
      //   return;
      // }
      if (state.password.isEmpty) {
        GSEasyLoading.showToast('please_enter_password'.tr());
        return;
      }

      if (state.captchaType == CaptchaType.picture && state.verificationCode == null) {
        GSEasyLoading.showToast('please_enter_verification_code'.tr());
        return;
      }
    }

    emit(state.copyWith(loginStatus: SimplyNetStatus.loading));

    if (state.captchaType == CaptchaType.wangYi && loginType == LoginType.userName) {
      _showWangYiCaptcha(
        account: state.username,
        onSuccess: (result) => performLoginApi(loginType, wangYiRes: result),
      );
    } else {
      performLoginApi(loginType);
    }
  }

  void performThirdAuthLoginApi(LoginType loginType, {required String token}) async {
    emit(state.copyWith(loginStatus: SimplyNetStatus.loading));
    final result = await UserApi.doLogin(loginType, thirdToken: token);
    if (result == null) {
      _loginFailed();
      return;
    }
    _loginSuccess(result);
  }

  void performLoginApi(LoginType loginType, {String? wangYiRes}) async {
    emit(state.copyWith(loginStatus: SimplyNetStatus.loading));

    final result = await UserApi.doLogin(
      loginType,
      username: loginType == LoginType.phone ? state.phone : state.username,
      password: loginType == LoginType.phone ? null : state.password,
      verificationCode: state.verificationCode,
      verificationUUID: state.captchaModel?.uuid,
      wangYiCaptcha: wangYiRes,
      smsCode: loginType == LoginType.phone ? state.smsCode : null,
      areaCode: state.selectedCountry?.areaCode,
    );
    state.verificationCodeController.text = '';
    fetchImageCaptcha();

    if (result == null) {
      _loginFailed();
      return;
    }
    _loginSuccess(result);
    // sl<NavigatorService>().popUntilOrPush(AppRouter.nav);
  }

  void _loginFailed() {
    emit(state.copyWith(loginStatus: SimplyNetStatus.failed));
  }

  void _loginSuccess(LoginTokenUser model) async {
    sl<UserCubit>().setLoginInfo(model);
    sl<MainScreenCubit>().fetchNoticeDialogListData(); // 获取系统公告弹窗
    _saveCredentials(); // 保存登录信息
    emit(state.copyWith(loginStatus: SimplyNetStatus.success));

    await Future.delayed(const Duration(milliseconds: 200));
    sl<NavigatorService>().pop();
  }

  void _saveCredentials() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (state.rememberMe) {
      await prefs.setString('login_username', state.username);
      await prefs.setString('login_password', state.password);
    } else {
      await prefs.remove('login_username');
      await prefs.remove('login_password');
    }
    await prefs.setBool('login_rememberMe', state.rememberMe);
  }

  bool isFetchingCaptcha = false;
  int _captchaRefreshCount = 0;
  DateTime? _lastRefreshTime;

  void fetchImageCaptcha() async {
    if (state.captchaType != CaptchaType.picture) return;

    if (_captchaRefreshCount >= 3) {
      final now = DateTime.now();
      if (_lastRefreshTime != null && now.difference(_lastRefreshTime!).inSeconds < 10) {
        GSEasyLoading.showToast("do_not_refresh_frequently".tr());
        return;
      }
    }

    if (isFetchingCaptcha) return;
    isFetchingCaptcha = true;
    _captchaRefreshCount++;
    _lastRefreshTime = DateTime.now();

    final result = await UserApi.fetchCaptcha();
    if (result != null && !isClosed) {
      emit(state.copyWith(captchaModel: result));
    }
    isFetchingCaptcha = false;
  }

  bool _isSwitching = false; // 添加切换状态标志位

  void switchLoginType() {
    // 如果正在切换中，直接返回
    if (_isSwitching) return;

    // 隐藏键盘
    sl<NavigatorService>().unFocus();

    _isSwitching = true; // 标记开始切换

    final newType = state.loginType == LoginType.userName ? LoginType.phone : LoginType.userName;

    // Clear text controllers
    state.usernameController.clear();
    state.phoneController.clear();
    state.passwordController.clear();
    state.verificationCodeController.clear();
    state.smsCodeController.clear();
    state.captchaController.clear();

    // Reset state values and update login type
    emit(state.copyWith(
      username: '',
      phone: '',
      password: '',
      smsCode: '',
      verificationCode: null,
      loginStatus: SimplyNetStatus.idle,
      loginType: newType,
    ));

    // 使用 Future.delayed 确保状态更新完成
    Future.delayed(const Duration(milliseconds: 300), () {
      _isSwitching = false; // 标记切换完成
    });
  }

  /// Update selected country
  void updateSelectedCountry(Country country) {
    emit(state.copyWith(selectedCountry: country));
  }

  /// Device login (Skip Login)
  Future<void> performDeviceLogin() async {
    try {
      emit(state.copyWith(loginStatus: SimplyNetStatus.loading));

      // Generate device fingerprint
      final fingerprintService = DeviceFingerprintService();
      final fingerprint = await fingerprintService.generateFingerprint();

      // Perform device login
      final result = await UserApi.doDeviceLogin(fingerprint.toJson());

      if (result != null) {
        _loginSuccess(result);
      } else {
        _loginFailed();
      }
    } catch (e) {
      _loginFailed();
    }
  }
}
