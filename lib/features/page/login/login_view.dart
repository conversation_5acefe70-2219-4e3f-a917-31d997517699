import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:wd/core/base/base_state.dart';

import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/auth_manager/auth_manager.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_checkbox.dart';

import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/verification_code/verification_code.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../shared/widgets/easy_loading.dart';
import 'auth/auth_cubit.dart';
import 'login_cubit.dart';
import 'login_state.dart';
import 'register/register_view.dart';
import 'register/register_cubit.dart';
import 'register/register_state.dart';
import 'package:wd/core/theme/themes.dart';

class LoginPage extends StatefulWidget {
  final String? inviteCode;
  final String? channelCode;

  const LoginPage({super.key, this.inviteCode, this.channelCode});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with HideFloatButtonRouteAwareMixin {
  /// Stores the captcha image as bytes
  Uint8List? imageBytes;

  /// Spacing constants for consistent layout
  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      body: BlocListener<LoginCubit, LoginState>(
        listenWhen: (previous, current) => previous.captchaModel?.img != current.captchaModel?.img,
        listener: (context, state) {
          final img = state.captchaModel?.img;
          if (img != null && img.isNotEmpty) {
            final base64String = img.split(',').last;
            setState(() => imageBytes = base64Decode(base64String));
          }
        },
        child: BlocBuilder<AuthCubit, AuthState>(
          builder: (context, state) {
            return _buildSliverLayout(state);
          },
        ),
      ),
    );
  }

  /// Builds the sliver layout with persistent header
  Widget _buildSliverLayout(AuthState state) {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36; // ~400px on 1252px screen

    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, loginState) {
        return BlocBuilder<RegisterCubit, RegisterState>(
          builder: (context, registerState) {
            // Get the appropriate login type based on auth type
            final currentLoginType = state.authType == AuthType.login
                ? loginState.loginType
                : (registerState.registerType ?? LoginType.userName);

            return CustomScrollView(
              slivers: [
                // Header section with logo and title
                SliverPersistentHeader(
                  pinned: false,
                  floating: false,
                  delegate: _LoginHeaderDelegate(
                    minHeight: headerHeight * 0.6, // Minimum height when collapsed
                    maxHeight: headerHeight, // Full height when expanded
                    title: _buildTitle(state.authType, currentLoginType),
                    subtitle: _buildSubtitle(state.authType, currentLoginType),
                  ),
                ),
                // Content section
                SliverToBoxAdapter(
                  child: Container(
                    decoration: BoxDecoration(
                      color: context.theme.scaffoldBackgroundColor,
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 40.gw),
                      child: Column(
                        children: [
                          _buildLoginTabs(),
                          SizedBox(height: _sectionSpacing.gw),
                          if (state.authType == AuthType.login)
                            _buildLogin()
                          else
                            RegisterPage(inviteCode: widget.inviteCode, channelCode: widget.channelCode),
                          20.verticalSpace,
                          if (state.authType == AuthType.register) ...[
                            _buildSocialDivider("or_register_with".tr()),
                            20.verticalSpace,
                          ],
                          _buildAuthBtn(),
                          40.verticalSpace,
                          if (state.authType == AuthType.login) buildBottomButtons(),
                          100.verticalSpace,
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildTitle(AuthType authType, LoginType loginType) {
    final isLogin = authType == AuthType.login;
    String loginMethod;

    switch (loginType) {
      case LoginType.phone:
        loginMethod = "phone".tr();
        break;
      case LoginType.email:
        loginMethod = "email".tr();
        break;
      case LoginType.userName:
      default:
        loginMethod = "username".tr();
        break;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        AneText(
          isLogin ? "login_with".tr() : "register_with".tr(),
          style: context.textTheme.regular.w700.ffAne.copyWith(fontSize: 46.fs),
        ),
        AneText(
          loginMethod,
          style: context.textTheme.primary.w700.ffAne.copyWith(fontSize: 46.fs),
          baseline: 25.fs,
        ),
      ],
    );
  }

  Widget _buildSubtitle(AuthType authType, LoginType loginType) {
    final isLogin = authType == AuthType.login;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "already_have_account".tr(),
          style: context.textTheme.title.fs16.ffAne,
        ),
        Text(
          "sign_in".tr(),
          style: isLogin
              ? context.textTheme.title.fs16.ffAne.copyWith(color: context.colorTheme.borderE)
              : context.textTheme.secondary.fs16.ffAne.copyWith(color: const Color(0xFFFFD600)),
        ),
      ],
    );
  }

  /// Builds the social divider for register
  Widget _buildSocialDivider(String text) {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            child: Center(child: Image.asset(Assets.lineLeft, fit: BoxFit.cover)),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: AneText(text, style: context.textTheme.title.fs16),
        ),
        Expanded(
          child: SizedBox(
            child: Center(child: Image.asset(Assets.lineRight, fit: BoxFit.cover)),
          ),
        ),
      ],
    );
  }

  _buildAuthBtn() {
    return InkWell(
      onTap: () async {
        final idToken = await AuthManager.signIn(LoginProvider.google);
        if (idToken != null && mounted) {
          context.read<LoginCubit>().performThirdAuthLoginApi(LoginType.google, token: idToken);
        }
      },
      child: Container(
        width: 94.gw,
        height: 46.gw,
        decoration: const BoxDecoration(color: Color(0xff212121), borderRadius: BorderRadius.all(Radius.circular(8))),
        alignment: Alignment.center,
        child: SvgPicture.asset(Assets.loginGoogle, width: 20.gw, height: 20.gw),
      ),
    );
  }

  /// 构建登录/注册切换标签
  /// Build login/register switch tabs
  Widget _buildLoginTabs() {
    final authType = context.watch<AuthCubit>().state.authType;
    final currentIndex = authType == AuthType.login ? 0 : 1;

    return Center(
      child: Container(
        width: 200.gw,
        height: 44.gw,
        decoration: BoxDecoration(
          color: context.colorTheme.foregroundColor,
          borderRadius: BorderRadius.circular(12.gw),
        ),
        padding: EdgeInsets.all(4.gw),
        child: Row(
          children: [
            _buildCustomTabItem("sign_in".tr(), Assets.loginTabIcon, currentIndex == 0, () {
              context.read<AuthCubit>().switchAuthType(AuthType.login);
            }),
            _buildCustomTabItem("sign_up".tr(), Assets.registerTabIcon, currentIndex == 1, () {
              context.read<AuthCubit>().switchAuthType(AuthType.register);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomTabItem(String title, String iconPath, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 100.gw,
          height: 36.gw,
          decoration: BoxDecoration(
            color: isSelected ? context.colorTheme.tabItemBgA : Colors.transparent,
            borderRadius: BorderRadius.circular(10.gw),
            border: isSelected ? Border.all(color: context.colorTheme.borderE, width: 1) : null,
          ),
          alignment: Alignment.center,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                iconPath,
                width: 16.gw,
                height: 16.gw,
              ),
              SizedBox(width: 6.gw),
              Text(
                title,
                style: isSelected
                    ? context.textTheme.primary.copyWith(color: context.theme.primaryColor)
                    : context.textTheme.title.copyWith(color: context.colorTheme.textTitle),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 根据登录类型构建对应的登录表单
  /// Build corresponding login form based on login type
  Widget _buildLogin() {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        // 如果当前登录类型不被支持，自动切换到支持的类型
        if (state.loginType == LoginType.phone && !state.authMethodType.supportsPhone) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<LoginCubit>().switchLoginType();
          });
          return const SizedBox();
        }
        if (state.loginType == LoginType.userName && !state.authMethodType.supportsAccount) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<LoginCubit>().switchLoginType();
          });
          return const SizedBox();
        }

        return state.loginType == LoginType.userName ? _buildPasswordLoginForm() : _buildPhoneLoginForm();
      },
    );
  }

  /// 构建登录按钮，包含加载状态
  /// Build login button with loading state
  Widget _buildLoginButton(LoginState state) {
    final isEnabled = kEnablePrivacyPolicySwitch ? state.acceptPrivacyPolicy : true;

    return CommonButton(
      title: "sign_in".tr(),
      textColor: context.colorTheme.btnTitlePrimary,
      showLoading: state.loginStatus == SimplyNetStatus.loading,
      onPressed: isEnabled
          ? () {
              // 获取当前登录类型
              // Get current login type
              final loginType = state.loginType;
              // 根据当前登录类型执行登录操作
              // Execute login operation based on current login type
              context.read<LoginCubit>().login(loginType);
            }
          : () {
              GSEasyLoading.showToast("please_accept_privacy_policy_toast".tr(), gravity: ToastGravity.BOTTOM);
            }, // Show toast when privacy policy not accepted
    );
  }

  /// 构建用户名密码登录表单
  /// Build username-password login form
  Widget _buildPasswordLoginForm() {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        return CommonScaleAnimationWidget(
          children: [
            _buildUsernameInput(state),
            SizedBox(height: _verticalSpacing.gw),
            _buildPasswordInput(state),
            SizedBox(height: _verticalSpacing.gw),
            if (state.captchaType == CaptchaType.picture) ...[
              _buildCaptchaInput(state),
              SizedBox(height: _verticalSpacing.gw),
            ],
            if (kEnablePrivacyPolicySwitch) ...[
              _buildPrivacyPolicySwitch(state),
              SizedBox(height: _sectionSpacing.gw),
            ],
            _buildLoginButton(state),
            SizedBox(height: _verticalSpacing.gw),
            _buildPasswordOptions(state),

            // 只有当配置支持手机号登录时才显示切换按钮
            if (state.authMethodType.supportsPhone) ...[
              SizedBox(height: 20.gw),
              buildLoginDivider("login_with_phone_number".tr()),
            ],
          ],
        );
      },
    );
  }

  /// Builds the username input field
  Widget _buildUsernameInput(LoginState state) {
    return IconTextfield(
      controller: state.usernameController,
      hintText: "enter_username_or_phone".tr(), // 请输入用户名/手机号,
      prefixIcon: IconButton(
        icon: Image.asset(
          Assets.iconLoginName,
          width: 20.gw,
          height: 20.gw,
        ),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<LoginCubit>().usernameChanged(value),
    );
  }

  /// Builds the password input field with visibility toggle
  Widget _buildPasswordInput(LoginState state) {
    return IconTextfield(
      controller: state.passwordController,
      hintText: "password_rule".tr(),
      prefixIcon: IconButton(
        icon: Image.asset(
          Assets.iconLoginPassword,
          width: 20.gw,
          height: 20.gw,
        ),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<LoginCubit>().passwordChanged(value),
    );
  }

  /// Builds the privacy policy acceptance switch
  Widget _buildPrivacyPolicySwitch(LoginState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Transform.scale(
          scale: 0.6,
          alignment: Alignment.centerLeft,
          child: Switch(
            value: state.acceptPrivacyPolicy,
            onChanged: (value) => context.read<LoginCubit>().toggleAcceptPrivacyPolicy(value),
            activeColor: context.theme.primaryColor,
            inactiveThumbColor: context.colorTheme.textSecondary,
            inactiveTrackColor: context.colorTheme.textSecondary.withOpacity(0.3),
          ),
        ),
        Expanded(
          child: Text(
            'accept_privacy_policy'.tr(),
            style: context.textTheme.title,
          ),
        ),
      ],
    );
  }

  /// Builds the "Remember me" checkbox and "Forgot password" link
  Widget _buildPasswordOptions(LoginState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CommonCheckbox(
          text: 'remember_password'.tr(),
          value: state.rememberMe,
          textStyle: context.textTheme.title,
          onChanged: (value) => context.read<LoginCubit>().toggleRememberMe(value),
        ),
        GestureDetector(
          // onTap: () => SystemUtil.contactService(),
          onTap: () => sl<NavigatorService>().push(AppRouter.forgot),
          child: Text(
            'forgot_password'.tr(),
            style: context.textTheme.title.copyWith(
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCaptchaInput(LoginState state) {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.only(top: 10.gw),
          child: IconTextfield(
            controller: state.captchaController,
            hintText: "verification_code".tr(),
            keyboardType: TextInputType.number,
            prefixIcon: _buildCaptchaPrefix(),
            suffixIcon: GestureDetector(
              onTap: () => context.read<LoginCubit>().fetchImageCaptcha(),
              behavior: HitTestBehavior.opaque,
              child: Container(
                alignment: Alignment.center,
                width: 100.gw,
                height: 45.gw,
                child: imageBytes != null
                    ? Image.memory(imageBytes!) // 只更新图片，不重新渲染 TextField
                    : Text('get_code'.tr(), style: Theme.of(context).textTheme.titleLarge),
              ),
            ),
            suffixPadding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 7.5.gh),
            onChanged: (value) => context.read<LoginCubit>().setVerificationCode(value),
          ),
        );
      },
    );
  }

  Widget _buildCaptchaPrefix() {
    return Padding(
      padding: EdgeInsets.all(18.gw),
      child: Image.asset(
        Assets.iconLoginShield,
        width: 20.gw,
        height: 24.gw,
      ),
    );
  }

  /// 构建手机验证码登录表单
  /// Build phone verification login form
  Widget _buildPhoneLoginForm() {
    return BlocBuilder<LoginCubit, LoginState>(
      builder: (context, state) {
        return CommonScaleAnimationWidget(
          children: [
            _buildPhoneInput(state),
            SizedBox(height: _verticalSpacing.gw),
            _buildGetCodeButton(state),
            SizedBox(height: _verticalSpacing.gw),
            if (state.captchaType == CaptchaType.picture) ...[
              _buildCaptchaInput(state),
              SizedBox(height: _verticalSpacing.gw),
            ],
            if (kEnablePrivacyPolicySwitch) ...[
              _buildPrivacyPolicySwitch(state),
              SizedBox(height: _sectionSpacing.gw),
            ],
            _buildLoginButton(state),

            // 只有当配置支持账号登录时才显示切换按钮
            if (state.authMethodType.supportsAccount) ...[
              SizedBox(height: 20.gw),
              buildLoginDivider("login_with_username".tr()),
            ],
          ],
        );
      },
    );
  }

  Widget _buildGetCodeButton(LoginState state) {
    return IconTextfield(
      controller: state.smsCodeController,
      hintText: "hint_enter_verification_code".tr(),
      keyboardType: TextInputType.number,
      prefixIcon: _buildVerificationPrefix(),
      suffixIcon: VerificationCode(
        phone: state.phone,
        areaCode: state.selectedCountry?.areaCode,
        checkIsBind: false,
        isGradient: false,
        onSmsCode: (smsCode) {
          if (kDebug && smsCode.isNotEmpty) {
            state.smsCodeController.text = smsCode;
            context.read<LoginCubit>().smsCodeChanged(smsCode);
          }
        },
      ),
      suffixPadding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 15.gh),
      onChanged: (value) => context.read<LoginCubit>().smsCodeChanged(value),
    );
  }

  Widget _buildVerificationPrefix() {
    return Padding(
      padding: EdgeInsets.all(18.gw),
      child: Image.asset(
        Assets.iconLoginShield,
        width: 20.gw,
        height: 24.gw,
      ),
    );
  }

  /// Builds the phone input field with country code
  Widget _buildPhoneInput(LoginState state) {
    return PhoneInputField(
      controller: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: state.selectedCountry,
      onCountryChanged: (country) {
        context.read<LoginCubit>().updateSelectedCountry(country);
      },
      onChanged: (value) => context.read<LoginCubit>().phoneChanged(value),
    );
  }

  /// Builds the username login divider with text button
  Widget buildLoginDivider(String text) {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            child: Center(child: Image.asset(Assets.lineLeft, fit: BoxFit.cover)),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: GestureDetector(
            onTap: () {
              // Switch to username login
              context.read<LoginCubit>().switchLoginType();
            },
            child: AneText(
              text,
              style: context.textTheme.title.fs16.copyWith(
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ),
        Expanded(
          child: SizedBox(
            child: Center(child: Image.asset(Assets.lineRight, fit: BoxFit.cover)),
          ),
        ),
      ],
    );
  }

  /// Builds the bottom action buttons
  Widget buildBottomButtons() {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CommonButton(
            style: CommonButtonStyle.tertiary,
            title: "skip_login".tr(),
            width: 150.gw,
            height: 50.gh,
            textColor: context.colorTheme.textSecondary,
            backgroundColor: context.colorTheme.foregroundColor,
            prefix: Icon(
              Icons.skip_next,
              color: context.colorTheme.textSecondary,
              size: 20.gw,
            ),
            onPressed: () => context.read<LoginCubit>().performDeviceLogin(),
          ),
          SizedBox(width: 8.gw), // 8px space between buttons
          CommonButton(
            style: CommonButtonStyle.tertiary,
            title: "help".tr(),
            width: 150.gw,
            height: 50.gh,
            textColor: context.colorTheme.textSecondary,
            backgroundColor: context.colorTheme.foregroundColor,
            prefix: Icon(
              Icons.help_outline,
              color: context.colorTheme.textSecondary,
              size: 20.gw,
            ),
            onPressed: () => SystemUtil.contactService(),
          ),
        ],
      ),
    );
  }
}

/// 登录类型切换的关键流程：
/// Key processes for login type switching:
///
/// 1. 顶部标签切换（登录/注册）：
///    Top tab switching (login/register):
///    - 调用 AuthCubit.switchAuthType 切换认证类型
///    - Call AuthCubit.switchAuthType to switch authentication type
///    - 同时重置登录和注册表单状态
///    - Simultaneously reset login and register form states
///
/// 2. 登录方式切换（密码/手机验证码）：
///    Login method switching (password/phone verification):
///    - 调用 AuthCubit.switchLoginType 切换登录类型
///    - Call AuthCubit.switchLoginType to switch login type
///    - 调用 LoginCubit.switchLoginType 重置表单状态
///    - Call LoginCubit.switchLoginType to reset form state
///    - 根据不同登录类型显示对应的表单界面
///    - Display corresponding form interface based on login type
///
/// 3. 登录操作：
///    Login operation:
///    - 获取当前登录类型
///    - Get current login type
///    - 根据登录类型调用对应的登录方法
///    - Call corresponding login method based on login type

/// Custom SliverPersistentHeaderDelegate for the login header
class _LoginHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget? title;
  final Widget? subtitle;

  _LoginHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = (shrinkOffset / (maxHeight - minHeight)).clamp(0.0, 1.0);
    final opacity = 1.0 - progress;

    return Container(
      height: maxHeight,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login/bg_login_logo.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Back button
          Positioned(
            top: ScreenUtil().statusBarHeight + 10.gw,
            left: 15.gw,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                alignment: Alignment.center,
                child: Image.asset(
                  Assets.iconBack,
                  height: 32.gh,
                  width: 32.gw,
                ),
              ),
            ),
          ),
          // Logo and title section
          Positioned.fill(
            child: Opacity(
              opacity: opacity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  90.verticalSpace,
                  // WD Logo
                  SvgPicture.asset(
                    Assets.tabLogo,
                    width: 160.gw,
                    height: 100.gh,
                  ),
                  20.verticalSpace,
                  // // Title
                  // if (title != null) title!,
                  // 8.verticalSpace,
                  // // Subtitle
                  // if (subtitle != null) subtitle!,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _LoginHeaderDelegate ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.title != title ||
        oldDelegate.subtitle != subtitle;
  }
}
