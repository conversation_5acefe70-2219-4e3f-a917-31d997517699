import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/account.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/apis/futures.dart';
import 'package:gp_stock_app/core/services/common_services.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/extension/future_list_extensions.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_state_v2.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/shared/routes/route_tracker.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/services/polling/polling_sevice_v2.dart';
import 'package:injectable/injectable.dart';

/// 账户页面主状态管理 Cubit
/// Main state management Cubit for the account screen
@singleton
class AccountScreenCubitV2 extends Cubit<AccountScreenStateV2> {
  /// 构造函数，初始化状态
  /// Constructor, initializes the state
  AccountScreenCubitV2() : super(const AccountScreenStateV2());

  void fetchData() async {
    await _updateMarketCategories();
    // fetchSpotScreenCurrentData(); // 现货
    // fetchContractSummaryPage(); // 合约
    startDataPolling();
  }

  /// 初始化市场分类，基于 fetchMarketOpenAsset 返回的可用市场
  /// Initialize market categories based on available markets from fetchMarketOpenAsset
  Future<void> _updateMarketCategories() async {
    try {
      TradingMode currentMode = AppConfig.instance.tradingMode;
      final sysConfig = await CommonServices.getSysConfig();
      if (sysConfig?.tradingMode != null) {
        currentMode = TradingModeExtension.fromIndex(sysConfig!.tradingMode);
      }

      // 期货模式默认开通，无需请求接口
      if (currentMode == TradingMode.futures) {
        final spotViewModels =
            currentMode.allCategories.map((category) => MarketCategoryState(category: category)).toList();
        emit(state.copyWith(spotViewModels: spotViewModels));
        return;
      }

      // 其他模式需要接口过滤
      final availableMarkets = await AccountApi.fetchMarketOpenAsset();

      final spotViewModels = currentMode.allCategories
          .where((category) => availableMarkets.contains(category.code))
          .map((category) => MarketCategoryState(category: category))
          .toList();

      emit(state.copyWith(spotViewModels: spotViewModels));
    } catch (e) {
      emit(state.copyWith(spotViewModels: []));
    } finally {
      _updateAccountTabBarList();
    }
  }

  void _updateAccountTabBarList() {
    if (state.spotViewModels.isEmpty) {
      emit(state.copyWith(accountTabBarList: [TradingAccountType.Contract]));
    } else {
      emit(state.copyWith(accountTabBarList: [TradingAccountType.Spot, TradingAccountType.Contract]));
    }
  }

  CancelToken? _positionCancelToken;
  CancelToken? _tradeCancelToken;
  CancelToken? _entrustCancelToken;

  /// 切换主Tab索引（现货、合约）
  /// Switch the main tab index (Spot, Contract)
  /// [newIndex]：tabBarList 的索引，对应主Tab
  void updateAccountTabBarIndex(int newIndex) {
    emit(state.copyWith(tradingTabBarCurrentIndex: newIndex));
  }

  /// 切换「现货账户」的主Tab索引（如A股、港股、美股等）
  /// Switch the main tab index (e.g. A-shares, HK stocks, US stocks, etc.)
  /// [newIndex]：spotViewModels 的索引，对应「现货账户」下的Tab
  void updateSpotScreenIndex(int newIndex) {
    emit(state.copyWith(spotScreenCurrentIndex: newIndex));
  }

  /// 切换「现货账户」的主Tab下的子Tab索引（如"当前持仓/成交明细/委托明细"）
  /// Switch the sub-tab index under the current main tab (e.g. "Holdings/Trades/Entrusts")
  /// [newIndex]：MarketCategoryState.selectedIndex 的新值，对应「现货账户」下的子Tab
  void updateSpotMarketSubScreenIndex(int newIndex) {
    // 复制一份新的 viewModel 列表，避免直接修改原状态
    // Copy a new list of viewModels to avoid mutating the original state
    final newViewModels = List<MarketCategoryState>.from(state.spotViewModels);
    // 获取当前主Tab对应的 MarketCategoryState
    // Get the MarketCategoryState for the current main tab
    final old = newViewModels[state.spotScreenCurrentIndex];
    // 用新的 selectedIndex 替换
    // Replace with new selectedIndex
    newViewModels[state.spotScreenCurrentIndex] = old.copyWith(selectedIndex: newIndex);
    emit(state.copyWith(spotViewModels: newViewModels));
  }

  /// 更新当前主Tab下某个明细类型的数据列表
  /// Update the data list for a specific detail type under the current main tab
  /// [type]：明细类型（如持仓、成交、委托）/ Detail type (e.g. holdings, trades, entrusts)
  /// [list]：新的数据列表 / New data list
  void updateSpotMarketDetail(MarketCategory category, OrderType type, OrderListState orderListState) {
    final newViewModels = List<MarketCategoryState>.from(state.spotViewModels);
    final index = newViewModels.indexWhere((e) => e.category.code == category.code);
    assert(index != -1, 'MarketCategoryCubit: 无法找到 category ${category.code}');
    if (index == -1) return;
    final old = newViewModels[index];
    newViewModels[index] = old.updateDetail(type, orderListState);
    emit(state.copyWith(spotViewModels: newViewModels));
  }

  /// 更新当前主Tab下的持仓资产/浮动盈亏
  /// Update the total assets / floating profit and loss for the current main tab
  ///
  /// [viewModel]：当前市场分类视图模型 / Current market category view model
  Future<void> fetchSpotAccountInfo({required MarketCategoryState viewModel}) async {
    final res = await AccountApi.fetchAccountInfo(category: viewModel.category);
    if (res != null) {
      // 创建更新后的视图模型，包含最新资产和浮动盈亏
      // Create updated view model with latest asset and PnL
      final newViewModels = List<MarketCategoryState>.from(state.spotViewModels);
      final index = newViewModels.indexWhere((e) => e.category.code == viewModel.category.code);
      if (index == -1) return;
      final old = newViewModels[index];
      final newViewModel = old.copyWith(
        totalAssets: res.assetAmount,
        floatingPnl: res.profitLoss,
      );

      // 更新状态中对应分类的数据
      // Update the corresponding market category in state
      updateSpotMarketCategoryState(viewModel: newViewModel);
    }
  }

  /// 替换 spotViewModels 中与指定 category 匹配的项
  /// Replace the item in [spotViewModels] that matches the given category
  ///
  /// [viewModel]：包含新数据的视图模型 / View model with updated data
  void updateSpotMarketCategoryState({required MarketCategoryState viewModel}) {
    emit(state.copyWith(
      spotViewModels: [
        for (final item in state.spotViewModels)
          if (item.category == viewModel.category) viewModel else item,
      ],
    ));
  }

  /// 拉取订单列表：当前持仓、委托明细、成交明细
  /// Fetch market order list
  Future<void> fetchMarketOrderList({
    required MarketCategory category,
    required OrderType type,
    required OrderListState orderListState,
    bool isLoadMore = false,
    bool isPolling = false,
  }) async {
    if (orderListState.status == DataStatus.loading) return;
    final newOrderListState = orderListState.copyWith(status: DataStatus.loading);
    updateSpotMarketDetail(category, type, newOrderListState);
    int page = 1;
    int pageSize = isPolling ? 100 : 20;
    if (isLoadMore) page = orderListState.page + 1;
    final bool isFetchPosition = type == OrderType.positions;

    CancelToken? cancelToken = _resetCancelToken(type);

    final result = isFetchPosition
        ? await ContractApi.getPositionList(
            page: page,
            pageSize: pageSize,
            commentAssetId: getIt<UserCubit>().state.accountInfo?.assetId.toString(),
            dataType: category.code,
            cancelToken: cancelToken,
          )
        : await ContractApi.getOrderList(
            page: page,
            pageSize: pageSize,
            commentAssetId: getIt<UserCubit>().state.accountInfo?.assetId.toString(),
            status: type == OrderType.trades ? 2 : 0,
            dataType: category.code,
            cancelToken: cancelToken,
          );
    if (result == null) {
      final newOrderListState = orderListState.copyWith(status: DataStatus.failed, page: page, hasMoreData: true);
      updateSpotMarketDetail(category, type, newOrderListState);

      return;
    }
    final data = result.records;
    final records = isLoadMore && !isPolling ? orderListState.records.mergeDedupKeepLast(data, (e) => e.id) : data;
    final tmp = newOrderListState.copyWith(
      records: records,
      status: DataStatus.success,
      page: page,
      hasMoreData: data.length >= pageSize,
      isInitialLoad: false,
    );
    updateSpotMarketDetail(category, type, tmp);
  }

  CancelToken _resetCancelToken(OrderType type) {
    switch (type) {
      case OrderType.positions:
        _positionCancelToken?.cancel();
        return _positionCancelToken = CancelToken();
      case OrderType.trades:
        _tradeCancelToken?.cancel();
        return _tradeCancelToken = CancelToken();
      case OrderType.order:
        _entrustCancelToken?.cancel();
        return _entrustCancelToken = CancelToken();
    }
  }

  /// 获取合约汇总页面数据，支持分页加载
  /// Fetches contract summary page data with pagination support
  ///
  /// [isLoadMore] - Whether this is a load more request / 是否为加载更多请求
  /// [isPolling] - Whether this is a polling request / 是否为轮询请求
  Future<void> fetchContractSummaryPage({
    bool isLoadMore = false,
    bool isPolling = false,
  }) async {
    if (!isPolling) emit(state.copyWith(contractSummaryStatus: DataStatus.loading));
    int page = 1;
    if (isLoadMore) page = state.contractSummaryPageNum + 1;
    final result = await ContractApi.fetchContractSummary(page: page);
    if (result == null) {
      emit(state.copyWith(contractSummaryStatus: DataStatus.failed, contractSummaryHaveMoreData: true));
      return;
    }
    final records = isLoadMore && !isPolling
        ? state.contractSummaryList.mergeDedupKeepLast(result.records, (e) => e.id)
        : result.records;
    emit(state.copyWith(
      contractSummaryList: records,
      contractSummaryPageNum: page,
      contractSummaryStatus: DataStatus.success,
      contractSummaryHaveMoreData: result.hasNext,
    ));
  }

  /// 刷新现货账户当前市场类型下的订单列表数据
  /// Refreshes spot account order list data for current market type
  void fetchSpotScreenCurrentData() async {
    if (state.spotViewModels.isEmpty) return;
    final currentViewModel = state.spotViewModels[state.spotScreenCurrentIndex];
    for (final entry in currentViewModel.details.entries) {
      fetchSpotAccountInfo(viewModel: currentViewModel);
      fetchMarketOrderList(
        category: currentViewModel.category,
        type: entry.key,
        orderListState: entry.value,
      );
    }
  }

  /// 设置期货仓位的止盈止损线
  /// Sets stop loss and take profit lines for futures position
  ///
  /// [positionId] - Position ID / 仓位ID
  /// [takeProfitValue] - Take profit price / 止盈价格
  /// [stopLossValue] - Stop loss price / 止损价格
  /// Returns success status / 返回设置是否成功
  Future<bool> setFuturesStopLine({
    positionId,
    double? takeProfitValue,
    double? stopLossValue,
  }) async {
    final flag = await FutureApi.setFStopLine(
        positionId: positionId, takeProfitValue: takeProfitValue, stopLossValue: stopLossValue);
    return flag;
  }

  /// 为期货仓位追加保证金
  /// Adds margin to futures position
  ///
  /// [positionId] - Position ID / 持仓id
  /// [amount] - Amount to add / 追加金额
  /// Returns success status / 返回追加是否成功
  Future<bool> addMargin({positionId, required double amount}) async {
    final flag = await FutureApi.addMargin(positionId: positionId, amount: amount);
    return flag;
  }

  /// 更新当前选中的现货订单记录，用于打开追加保证金页面持续刷新
  /// Updates currently selected spot order record
  ///
  /// [record] - Order record to select / 要选中的订单记录
  void updateCurrentSelectSpotOrder(FTradeAcctOrderRecords? record) {
    emit(state.copyWith(currentSelectSpotOrder: () => record));
  }

  /// 数据轮询
  /// DataPolling
  void startDataPolling() async {
    getIt<PollingServiceV2>().startPolling(
      id: kGPAccountScreenDataPolling,
      onPoll: () async {
        if (state.tradingTabBarCurrentIndex == 0) {
          // 现货
          fetchSpotScreenCurrentData();
        } else {
          // 合约
          fetchContractSummaryPage(isPolling: true);
        }
        return true;
      },
      interval: const Duration(seconds: 1),
      shouldPause: () => !_isAccountPageShowing(),
      shouldStop: () => !getIt<UserCubit>().state.isLogin,
    );
  }

  /// 判断是否处于「账户」界面
  /// Determine whether the current visible screen is the "Account" page
  ///
  /// Returns true if both the current route is the root route
  /// and the selected tab is the "Account" tab.
  bool _isAccountPageShowing() {
    // 当前页面是否处于根路由（Root route）
    bool isAtRootRoute = RouteTracker().getCurrentRouteName() == routeRoot;
    // 当前底部导航选中的是否为 账户页
    bool isAtAccountPage = getIt<MainCubit>().state.selectedNavigationItem == NavigationItem.account;
    return isAtRootRoute && isAtAccountPage;
  }
}
