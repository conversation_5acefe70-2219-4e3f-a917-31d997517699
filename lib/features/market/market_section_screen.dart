import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/index_trade_screen.dart';
import 'package:gp_stock_app/features/market/stock_screen.dart';
import 'package:gp_stock_app/features/market/watch_list/screens/watch_list_screen.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/shared/widgets/tab/custom_tab_bar.dart';

import '../../shared/mixin/animation.dart';
import 'logic/market/market_cubit.dart';

class MarketSectionScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;
  const MarketSectionScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<MarketSectionScreen> createState() => _MarketSectionScreenState();
}

class _MarketSectionScreenState extends State<MarketSectionScreen>
    with StaggeredAnimation, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  List<String> tabs = [];

  @override
  void initState() {
    super.initState();
    TradingMode currentMode = AppConfig.instance.tradingMode;
    final sysSettingsState = context.read<SysSettingsCubit>().state;

    sysSettingsState.maybeWhen(
      loaded: (_, config) {
        currentMode = TradingModeExtension.fromIndex(config.tradingMode);
      },
      orElse: () {},
    );

    switch (currentMode) {
      case TradingMode.stock:
        tabs = ['marketTitle6'.tr(), 'marketTitle3'.tr(), 'marketTitle1'.tr()];
        break;
      case TradingMode.futures:
        tabs = ['marketTitle7'.tr(), 'marketTitle1'.tr()];
        break;
      case TradingMode.stockAndFutures:
        tabs = ['marketTitle6'.tr(), 'marketTitle3'.tr(), 'marketTitle7'.tr(), 'marketTitle1'.tr()];
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: Column(
        children: [
          BlocSelector<MarketCubit, MarketState, int>(
            selector: (state) => state.mainHeaderIndex,
            builder: (context, state) {
              return CustomTabBar(
                tabs: tabs,
                selectedIndex: state,
                onTabSelected: (index) => context.read<MarketCubit>().updateMainHeaderTab(index),
              );
            },
          ),
          Expanded(
            child: BlocSelector<MarketCubit, MarketState, int>(
              selector: (state) => state.mainHeaderIndex,
              builder: (context, idx) {
                final title = tabs[idx];
                if (title == 'marketTitle6'.tr()) {
                  return StockScreen(
                    showBackButton: true,
                    scrollController: widget.scrollController,
                  );
                }
                if (title == 'marketTitle3'.tr()) {
                  return IndexTradeScreen();
                }
                if (title == 'marketTitle7'.tr()) {
                  return MultiBlocProvider(
                    providers: [
                      BlocProvider<FTradeListCubit>(
                        create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: false),
                      ),
                      BlocProvider<MainCubit>(create: (_) => getIt<MainCubit>()),
                    ],
                    child: FTradeListScreen(showInHomePage: false),
                  );
                }
                if (title == 'marketTitle1'.tr()) {
                  return WatchListScreen();
                }
                return const StockScreen();
              },
            ),
          ),
        ],
      ),
    );
  }
}
