import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/trading_center_screen.dart';
import 'package:gp_stock_app/features/market/domain/models/index_stock/index_stock.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/market/widgets/graph_card_selection.dart';
import 'package:gp_stock_app/features/market/widgets/sliver_app_bar_delegate.dart';
import 'package:gp_stock_app/features/market/widgets/visual_graph_section.dart';

import '../../shared/constants/enums.dart';
import '../../shared/models/instrument/instrument.dart';

class IndexTradeScreen extends StatefulWidget {
  const IndexTradeScreen({super.key});

  @override
  State<IndexTradeScreen> createState() => _IndexTradeScreenState();
}

class _IndexTradeScreenState extends State<IndexTradeScreen> with WidgetsBindingObserver {
  AccountCubit? _accountCubit;
  int _appLifecycleStatePausedTimeSecond = 0;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    _accountCubit?.stopTradingCenterPolling();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    int now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    if (state == AppLifecycleState.resumed && (now - _appLifecycleStatePausedTimeSecond) > 30) {
      getIt<IndexTradeCubit>().reloadTimeline();
    } else if (state == AppLifecycleState.paused) {
      _appLifecycleStatePausedTimeSecond = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<IndexTradeCubit, IndexTradeState,
        ({int selectedIndex, List<IndexStock> indexes, List<IndexStockInfo> indexStocks})>(
      selector: (state) => (selectedIndex: state.selectedIndex, indexes: state.indexes, indexStocks: state.indexStocks),
      builder: (context, state) {
        // Early validation to prevent index out of range
        if (state.indexes.isEmpty || state.indexStocks.isEmpty) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        // Make sure selectedIndex is within bounds
        final safeIndex = state.selectedIndex.clamp(0, state.indexes.length - 1);

        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => TradingCubit()
                ..setIndexTrading(isIndexTrading: true)
                ..setStockInfo(state.indexStocks[safeIndex].stockInfo.data)
                ..getCalculateConfig(
                  market: state.indexes[safeIndex].market,
                  securityType: state.indexes[safeIndex].securityType,
                  chargePackageId: null,
                )
                ..setIndexStockData(
                  allowShortSell: state.indexes[safeIndex].allowShortSell,
                  lotSize: state.indexes[safeIndex].lotSize,
                  tradeUnit: state.indexes[safeIndex].tradeUnit,
                ),
            ),
            BlocProvider.value(
              value: context.read<MarketCubit>()..init(),
            ),
            BlocProvider(
              create: (context) => getIt<AccountCubit>()
                ..startTradingCenterPolling(Instrument(instrument: state.indexes[state.selectedIndex].instrument))
                ..getOrderList(
                  AccountMarketType.currentPositions,
                  symbol: state.indexes[state.selectedIndex].symbol,
                  market: state.indexes[state.selectedIndex].market,
                  securityType: state.indexes[state.selectedIndex].securityType,
                )
                ..getContractSummary()
                ..getOrderList(
                  AccountMarketType.orderDetails,
                  symbol: state.indexes[state.selectedIndex].symbol,
                  status: 0,
                  market: state.indexes[state.selectedIndex].market,
                  securityType: state.indexes[state.selectedIndex].securityType,
                ),
            ),
          ],
          child: Builder(builder: (context) {
            _accountCubit = context.read<AccountCubit>();
            final tradingCubit = context.read<TradingCubit>();
            return MultiBlocListener(
              listeners: [
                BlocListener<TradingCubit, TradingState>(
                  listenWhen: (previous, current) =>
                      previous.availableQuantity != current.availableQuantity &&
                      current.tradeDirection != TradeDirection.sell,
                  listener: (context, state) => tradingCubit.setQuantity(state.availableQuantity),
                ),
                BlocListener<IndexTradeCubit, IndexTradeState>(
                  listenWhen: (previous, current) => previous.selectedIndex != current.selectedIndex,
                  listener: (context, state) {
                    context.read<MarketCubit>().init();
                    context.read<AccountCubit>().startTradingCenterPolling(
                        Instrument(instrument: state.indexes[state.selectedIndex].instrument));
                    tradingCubit
                      ..getCalculateConfig(
                        market: state.indexes[state.selectedIndex].market,
                        securityType: state.indexes[state.selectedIndex].securityType,
                        chargePackageId: null,
                      )
                      ..setStockInfo(state.indexStocks[state.selectedIndex].stockInfo.data);
                  },
                ),
                // Listens for changes in the allowShortSell property of the selected index
                // and updates the TradingCubit's shortSell setting accordingly
                BlocListener<IndexTradeCubit, IndexTradeState>(
                  listenWhen: (previous, current) =>
                      previous.indexes[previous.selectedIndex].allowShortSell !=
                      current.indexes[current.selectedIndex].allowShortSell,
                  listener: (context, state) => tradingCubit.setIndexStockData(
                    allowShortSell: state.indexes[state.selectedIndex].allowShortSell,
                    lotSize: state.indexes[state.selectedIndex].lotSize,
                    tradeUnit: state.indexes[state.selectedIndex].tradeUnit,
                  ),
                ),
              ],
              child: PopScope(
                child: CustomScrollView(
                  slivers: [
                    SliverPersistentHeader(
                      delegate: SliverAppBarDelegate(
                        minHeight: 42.gh,
                        maxHeight: 42.gh,
                        child: Container(
                          color: context.theme.scaffoldBackgroundColor,
                          child: const GraphCardSelection(),
                        ),
                      ),
                      pinned: true,
                    ),
                    SliverToBoxAdapter(
                      child: VisualGraphSection(),
                    ),
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: TradingCenterView(
                        instrument: state.indexes[state.selectedIndex].instrumentObj,
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: SizedBox(height: kBottomNavigationBarHeight * 2),
                    ),
                  ],
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
