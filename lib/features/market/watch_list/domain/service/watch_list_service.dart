import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/models/watch_list_response.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/models/watch_model.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/repository/watch_list_repo.dart';
import 'package:injectable/injectable.dart';

@Injectable(as: WatchListRepository)
class WatchListService implements WatchListRepository {
  @override
  Future<ResponseResult<bool>> addToWatchList({
    required String symbol,
    required String market,
    required int putSort,
    required String securityType,
    required int sort,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.addToWatchList,
        data: {
          "symbol": symbol,
          "market": market,
          "putSort": putSort,
          "securityType": securityType,
          "sort": sort,
        },
        isAuthRequired: true,
      );
      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: response.data['msg']);
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> removeFromWatchList(int choiceId) async {
    try {
      final response = await NetworkProvider().delete(
        ApiEndpoints.removeFromWatchList,
        queryParameters: {"id": choiceId},
        isAuthRequired: true,
      );
      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: response.data['msg']);
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  @override
  Future<ResponseResult<WatchListResponse>> getWatchList({
    int? pageNumber,
    int? pageSize,
    String? market,
  }) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.getWatchList,
        queryParameters: {
          'pageNumber': pageNumber,
          'pageSize': pageSize,
          'market': market,
        },
        isAuthRequired: true,
      );
      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: WatchListResponse.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: response.data['msg']);
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }


  @override
  Future<ResponseResult<WatchModel>> getWatchListDetail(String choiceId) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.getWatchListDetail,
        data: {"id": choiceId},
        isAuthRequired: true,
      );
      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: WatchModel.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: response.data['msg']);
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}
