import 'package:freezed_annotation/freezed_annotation.dart';

part 'watch_model.freezed.dart';
part 'watch_model.g.dart';

@freezed
class WatchModel with _$WatchModel {
  const WatchModel._();
  const factory WatchModel({
    String? createTime,
    required int id,
    required String instrument,
    required String market,
    required String name,
    required int putSort,
    required String securityType,
    required int sort,
    required String symbol,
    String? updateTime,
    int? userId,
    @Default(0) double gain,
    required double latestPrice,
  }) = _WatchModel;

  factory WatchModel.fromJson(Map<String, dynamic> json) => _$WatchModelFromJson(json);

  factory WatchModel.dummy() => WatchModel(
        createTime: '',
        id: 0,
        instrument: '',
        market: '',
        name: '',
        putSort: 0,
        securityType: '',
        sort: 0,
        symbol: '',
        updateTime: '',
        userId: 0,
        gain: 0,
        latestPrice: 0,
      );
}
