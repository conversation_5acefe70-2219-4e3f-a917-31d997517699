// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'watch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WatchModelImpl _$$WatchModelImplFromJson(Map<String, dynamic> json) =>
    _$WatchModelImpl(
      createTime: json['createTime'] as String?,
      id: (json['id'] as num).toInt(),
      instrument: json['instrument'] as String,
      market: json['market'] as String,
      name: json['name'] as String,
      putSort: (json['putSort'] as num).toInt(),
      securityType: json['securityType'] as String,
      sort: (json['sort'] as num).toInt(),
      symbol: json['symbol'] as String,
      updateTime: json['updateTime'] as String?,
      userId: (json['userId'] as num?)?.toInt(),
      gain: (json['gain'] as num?)?.toDouble() ?? 0,
      latestPrice: (json['latestPrice'] as num).toDouble(),
    );

Map<String, dynamic> _$$WatchModelImplToJson(_$WatchModelImpl instance) =>
    <String, dynamic>{
      'createTime': instance.createTime,
      'id': instance.id,
      'instrument': instance.instrument,
      'market': instance.market,
      'name': instance.name,
      'putSort': instance.putSort,
      'securityType': instance.securityType,
      'sort': instance.sort,
      'symbol': instance.symbol,
      'updateTime': instance.updateTime,
      'userId': instance.userId,
      'gain': instance.gain,
      'latestPrice': instance.latestPrice,
    };
