// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'watch_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WatchModel _$WatchModelFromJson(Map<String, dynamic> json) {
  return _WatchModel.fromJson(json);
}

/// @nodoc
mixin _$WatchModel {
  String? get createTime => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  String get instrument => throw _privateConstructorUsedError;
  String get market => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get putSort => throw _privateConstructorUsedError;
  String get securityType => throw _privateConstructorUsedError;
  int get sort => throw _privateConstructorUsedError;
  String get symbol => throw _privateConstructorUsedError;
  String? get updateTime => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;
  double get gain => throw _privateConstructorUsedError;
  double get latestPrice => throw _privateConstructorUsedError;

  /// Serializes this WatchModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WatchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WatchModelCopyWith<WatchModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WatchModelCopyWith<$Res> {
  factory $WatchModelCopyWith(
          WatchModel value, $Res Function(WatchModel) then) =
      _$WatchModelCopyWithImpl<$Res, WatchModel>;
  @useResult
  $Res call(
      {String? createTime,
      int id,
      String instrument,
      String market,
      String name,
      int putSort,
      String securityType,
      int sort,
      String symbol,
      String? updateTime,
      int? userId,
      double gain,
      double latestPrice});
}

/// @nodoc
class _$WatchModelCopyWithImpl<$Res, $Val extends WatchModel>
    implements $WatchModelCopyWith<$Res> {
  _$WatchModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WatchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = freezed,
    Object? id = null,
    Object? instrument = null,
    Object? market = null,
    Object? name = null,
    Object? putSort = null,
    Object? securityType = null,
    Object? sort = null,
    Object? symbol = null,
    Object? updateTime = freezed,
    Object? userId = freezed,
    Object? gain = null,
    Object? latestPrice = null,
  }) {
    return _then(_value.copyWith(
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      instrument: null == instrument
          ? _value.instrument
          : instrument // ignore: cast_nullable_to_non_nullable
              as String,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      putSort: null == putSort
          ? _value.putSort
          : putSort // ignore: cast_nullable_to_non_nullable
              as int,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      sort: null == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      gain: null == gain
          ? _value.gain
          : gain // ignore: cast_nullable_to_non_nullable
              as double,
      latestPrice: null == latestPrice
          ? _value.latestPrice
          : latestPrice // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WatchModelImplCopyWith<$Res>
    implements $WatchModelCopyWith<$Res> {
  factory _$$WatchModelImplCopyWith(
          _$WatchModelImpl value, $Res Function(_$WatchModelImpl) then) =
      __$$WatchModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? createTime,
      int id,
      String instrument,
      String market,
      String name,
      int putSort,
      String securityType,
      int sort,
      String symbol,
      String? updateTime,
      int? userId,
      double gain,
      double latestPrice});
}

/// @nodoc
class __$$WatchModelImplCopyWithImpl<$Res>
    extends _$WatchModelCopyWithImpl<$Res, _$WatchModelImpl>
    implements _$$WatchModelImplCopyWith<$Res> {
  __$$WatchModelImplCopyWithImpl(
      _$WatchModelImpl _value, $Res Function(_$WatchModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of WatchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = freezed,
    Object? id = null,
    Object? instrument = null,
    Object? market = null,
    Object? name = null,
    Object? putSort = null,
    Object? securityType = null,
    Object? sort = null,
    Object? symbol = null,
    Object? updateTime = freezed,
    Object? userId = freezed,
    Object? gain = null,
    Object? latestPrice = null,
  }) {
    return _then(_$WatchModelImpl(
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      instrument: null == instrument
          ? _value.instrument
          : instrument // ignore: cast_nullable_to_non_nullable
              as String,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      putSort: null == putSort
          ? _value.putSort
          : putSort // ignore: cast_nullable_to_non_nullable
              as int,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      sort: null == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      gain: null == gain
          ? _value.gain
          : gain // ignore: cast_nullable_to_non_nullable
              as double,
      latestPrice: null == latestPrice
          ? _value.latestPrice
          : latestPrice // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WatchModelImpl extends _WatchModel {
  const _$WatchModelImpl(
      {this.createTime,
      required this.id,
      required this.instrument,
      required this.market,
      required this.name,
      required this.putSort,
      required this.securityType,
      required this.sort,
      required this.symbol,
      this.updateTime,
      this.userId,
      this.gain = 0,
      required this.latestPrice})
      : super._();

  factory _$WatchModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WatchModelImplFromJson(json);

  @override
  final String? createTime;
  @override
  final int id;
  @override
  final String instrument;
  @override
  final String market;
  @override
  final String name;
  @override
  final int putSort;
  @override
  final String securityType;
  @override
  final int sort;
  @override
  final String symbol;
  @override
  final String? updateTime;
  @override
  final int? userId;
  @override
  @JsonKey()
  final double gain;
  @override
  final double latestPrice;

  @override
  String toString() {
    return 'WatchModel(createTime: $createTime, id: $id, instrument: $instrument, market: $market, name: $name, putSort: $putSort, securityType: $securityType, sort: $sort, symbol: $symbol, updateTime: $updateTime, userId: $userId, gain: $gain, latestPrice: $latestPrice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WatchModelImpl &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.instrument, instrument) ||
                other.instrument == instrument) &&
            (identical(other.market, market) || other.market == market) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.putSort, putSort) || other.putSort == putSort) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.gain, gain) || other.gain == gain) &&
            (identical(other.latestPrice, latestPrice) ||
                other.latestPrice == latestPrice));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      createTime,
      id,
      instrument,
      market,
      name,
      putSort,
      securityType,
      sort,
      symbol,
      updateTime,
      userId,
      gain,
      latestPrice);

  /// Create a copy of WatchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WatchModelImplCopyWith<_$WatchModelImpl> get copyWith =>
      __$$WatchModelImplCopyWithImpl<_$WatchModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WatchModelImplToJson(
      this,
    );
  }
}

abstract class _WatchModel extends WatchModel {
  const factory _WatchModel(
      {final String? createTime,
      required final int id,
      required final String instrument,
      required final String market,
      required final String name,
      required final int putSort,
      required final String securityType,
      required final int sort,
      required final String symbol,
      final String? updateTime,
      final int? userId,
      final double gain,
      required final double latestPrice}) = _$WatchModelImpl;
  const _WatchModel._() : super._();

  factory _WatchModel.fromJson(Map<String, dynamic> json) =
      _$WatchModelImpl.fromJson;

  @override
  String? get createTime;
  @override
  int get id;
  @override
  String get instrument;
  @override
  String get market;
  @override
  String get name;
  @override
  int get putSort;
  @override
  String get securityType;
  @override
  int get sort;
  @override
  String get symbol;
  @override
  String? get updateTime;
  @override
  int? get userId;
  @override
  double get gain;
  @override
  double get latestPrice;

  /// Create a copy of WatchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WatchModelImplCopyWith<_$WatchModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
