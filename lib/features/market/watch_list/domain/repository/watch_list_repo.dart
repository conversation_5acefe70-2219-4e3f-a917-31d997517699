import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/models/watch_list_response.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/models/watch_model.dart';

abstract interface class WatchListRepository {
  Future<ResponseResult<bool>> addToWatchList({
    required String symbol,
    required String market,
    required int putSort,
    required String securityType,
    required int sort,
  });

  Future<ResponseResult<bool>> removeFromWatchList(int choiceId);

  Future<ResponseResult<WatchListResponse>> getWatchList({
    int? pageNumber,
    int? pageSize,
    String? market,
  });

  Future<ResponseResult<WatchModel>> getWatchListDetail(String choiceId);

}
