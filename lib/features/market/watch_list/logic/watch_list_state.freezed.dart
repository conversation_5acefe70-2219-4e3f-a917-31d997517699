// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'watch_list_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WatchListState {
  DataStatus get addToWatchListStatus => throw _privateConstructorUsedError;
  DataStatus get removeFromWatchListStatus =>
      throw _privateConstructorUsedError;
  DataStatus get getWatchListStatus => throw _privateConstructorUsedError;
  DataStatus get getWatchListDetailStatus => throw _privateConstructorUsedError;
  DataStatus get getWatchListByInstrumentStatus =>
      throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  List<WatchModel>? get watchList => throw _privateConstructorUsedError;
  WatchModel? get watchListDetail => throw _privateConstructorUsedError;
  WatchlistItemEntity? get watchListByInstrument =>
      throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get totalPages => throw _privateConstructorUsedError;
  bool get hasMore => throw _privateConstructorUsedError;
  bool get isLoadMore => throw _privateConstructorUsedError;
  SortType? get sortByPriceAsc => throw _privateConstructorUsedError;
  SortType? get sortByChangeAsc => throw _privateConstructorUsedError;

  /// Create a copy of WatchListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WatchListStateCopyWith<WatchListState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WatchListStateCopyWith<$Res> {
  factory $WatchListStateCopyWith(
          WatchListState value, $Res Function(WatchListState) then) =
      _$WatchListStateCopyWithImpl<$Res, WatchListState>;
  @useResult
  $Res call(
      {DataStatus addToWatchListStatus,
      DataStatus removeFromWatchListStatus,
      DataStatus getWatchListStatus,
      DataStatus getWatchListDetailStatus,
      DataStatus getWatchListByInstrumentStatus,
      String? error,
      List<WatchModel>? watchList,
      WatchModel? watchListDetail,
      WatchlistItemEntity? watchListByInstrument,
      int currentPage,
      int totalPages,
      bool hasMore,
      bool isLoadMore,
      SortType? sortByPriceAsc,
      SortType? sortByChangeAsc});

  $WatchModelCopyWith<$Res>? get watchListDetail;
}

/// @nodoc
class _$WatchListStateCopyWithImpl<$Res, $Val extends WatchListState>
    implements $WatchListStateCopyWith<$Res> {
  _$WatchListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WatchListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addToWatchListStatus = null,
    Object? removeFromWatchListStatus = null,
    Object? getWatchListStatus = null,
    Object? getWatchListDetailStatus = null,
    Object? getWatchListByInstrumentStatus = null,
    Object? error = freezed,
    Object? watchList = freezed,
    Object? watchListDetail = freezed,
    Object? watchListByInstrument = freezed,
    Object? currentPage = null,
    Object? totalPages = null,
    Object? hasMore = null,
    Object? isLoadMore = null,
    Object? sortByPriceAsc = freezed,
    Object? sortByChangeAsc = freezed,
  }) {
    return _then(_value.copyWith(
      addToWatchListStatus: null == addToWatchListStatus
          ? _value.addToWatchListStatus
          : addToWatchListStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      removeFromWatchListStatus: null == removeFromWatchListStatus
          ? _value.removeFromWatchListStatus
          : removeFromWatchListStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      getWatchListStatus: null == getWatchListStatus
          ? _value.getWatchListStatus
          : getWatchListStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      getWatchListDetailStatus: null == getWatchListDetailStatus
          ? _value.getWatchListDetailStatus
          : getWatchListDetailStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      getWatchListByInstrumentStatus: null == getWatchListByInstrumentStatus
          ? _value.getWatchListByInstrumentStatus
          : getWatchListByInstrumentStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      watchList: freezed == watchList
          ? _value.watchList
          : watchList // ignore: cast_nullable_to_non_nullable
              as List<WatchModel>?,
      watchListDetail: freezed == watchListDetail
          ? _value.watchListDetail
          : watchListDetail // ignore: cast_nullable_to_non_nullable
              as WatchModel?,
      watchListByInstrument: freezed == watchListByInstrument
          ? _value.watchListByInstrument
          : watchListByInstrument // ignore: cast_nullable_to_non_nullable
              as WatchlistItemEntity?,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadMore: null == isLoadMore
          ? _value.isLoadMore
          : isLoadMore // ignore: cast_nullable_to_non_nullable
              as bool,
      sortByPriceAsc: freezed == sortByPriceAsc
          ? _value.sortByPriceAsc
          : sortByPriceAsc // ignore: cast_nullable_to_non_nullable
              as SortType?,
      sortByChangeAsc: freezed == sortByChangeAsc
          ? _value.sortByChangeAsc
          : sortByChangeAsc // ignore: cast_nullable_to_non_nullable
              as SortType?,
    ) as $Val);
  }

  /// Create a copy of WatchListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WatchModelCopyWith<$Res>? get watchListDetail {
    if (_value.watchListDetail == null) {
      return null;
    }

    return $WatchModelCopyWith<$Res>(_value.watchListDetail!, (value) {
      return _then(_value.copyWith(watchListDetail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WatchListStateImplCopyWith<$Res>
    implements $WatchListStateCopyWith<$Res> {
  factory _$$WatchListStateImplCopyWith(_$WatchListStateImpl value,
          $Res Function(_$WatchListStateImpl) then) =
      __$$WatchListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus addToWatchListStatus,
      DataStatus removeFromWatchListStatus,
      DataStatus getWatchListStatus,
      DataStatus getWatchListDetailStatus,
      DataStatus getWatchListByInstrumentStatus,
      String? error,
      List<WatchModel>? watchList,
      WatchModel? watchListDetail,
      WatchlistItemEntity? watchListByInstrument,
      int currentPage,
      int totalPages,
      bool hasMore,
      bool isLoadMore,
      SortType? sortByPriceAsc,
      SortType? sortByChangeAsc});

  @override
  $WatchModelCopyWith<$Res>? get watchListDetail;
}

/// @nodoc
class __$$WatchListStateImplCopyWithImpl<$Res>
    extends _$WatchListStateCopyWithImpl<$Res, _$WatchListStateImpl>
    implements _$$WatchListStateImplCopyWith<$Res> {
  __$$WatchListStateImplCopyWithImpl(
      _$WatchListStateImpl _value, $Res Function(_$WatchListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WatchListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addToWatchListStatus = null,
    Object? removeFromWatchListStatus = null,
    Object? getWatchListStatus = null,
    Object? getWatchListDetailStatus = null,
    Object? getWatchListByInstrumentStatus = null,
    Object? error = freezed,
    Object? watchList = freezed,
    Object? watchListDetail = freezed,
    Object? watchListByInstrument = freezed,
    Object? currentPage = null,
    Object? totalPages = null,
    Object? hasMore = null,
    Object? isLoadMore = null,
    Object? sortByPriceAsc = freezed,
    Object? sortByChangeAsc = freezed,
  }) {
    return _then(_$WatchListStateImpl(
      addToWatchListStatus: null == addToWatchListStatus
          ? _value.addToWatchListStatus
          : addToWatchListStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      removeFromWatchListStatus: null == removeFromWatchListStatus
          ? _value.removeFromWatchListStatus
          : removeFromWatchListStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      getWatchListStatus: null == getWatchListStatus
          ? _value.getWatchListStatus
          : getWatchListStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      getWatchListDetailStatus: null == getWatchListDetailStatus
          ? _value.getWatchListDetailStatus
          : getWatchListDetailStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      getWatchListByInstrumentStatus: null == getWatchListByInstrumentStatus
          ? _value.getWatchListByInstrumentStatus
          : getWatchListByInstrumentStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      watchList: freezed == watchList
          ? _value._watchList
          : watchList // ignore: cast_nullable_to_non_nullable
              as List<WatchModel>?,
      watchListDetail: freezed == watchListDetail
          ? _value.watchListDetail
          : watchListDetail // ignore: cast_nullable_to_non_nullable
              as WatchModel?,
      watchListByInstrument: freezed == watchListByInstrument
          ? _value.watchListByInstrument
          : watchListByInstrument // ignore: cast_nullable_to_non_nullable
              as WatchlistItemEntity?,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadMore: null == isLoadMore
          ? _value.isLoadMore
          : isLoadMore // ignore: cast_nullable_to_non_nullable
              as bool,
      sortByPriceAsc: freezed == sortByPriceAsc
          ? _value.sortByPriceAsc
          : sortByPriceAsc // ignore: cast_nullable_to_non_nullable
              as SortType?,
      sortByChangeAsc: freezed == sortByChangeAsc
          ? _value.sortByChangeAsc
          : sortByChangeAsc // ignore: cast_nullable_to_non_nullable
              as SortType?,
    ));
  }
}

/// @nodoc

class _$WatchListStateImpl implements _WatchListState {
  const _$WatchListStateImpl(
      {this.addToWatchListStatus = DataStatus.idle,
      this.removeFromWatchListStatus = DataStatus.idle,
      this.getWatchListStatus = DataStatus.idle,
      this.getWatchListDetailStatus = DataStatus.idle,
      this.getWatchListByInstrumentStatus = DataStatus.idle,
      this.error,
      final List<WatchModel>? watchList,
      this.watchListDetail,
      this.watchListByInstrument,
      this.currentPage = 1,
      this.totalPages = 1,
      this.hasMore = true,
      this.isLoadMore = false,
      this.sortByPriceAsc,
      this.sortByChangeAsc})
      : _watchList = watchList;

  @override
  @JsonKey()
  final DataStatus addToWatchListStatus;
  @override
  @JsonKey()
  final DataStatus removeFromWatchListStatus;
  @override
  @JsonKey()
  final DataStatus getWatchListStatus;
  @override
  @JsonKey()
  final DataStatus getWatchListDetailStatus;
  @override
  @JsonKey()
  final DataStatus getWatchListByInstrumentStatus;
  @override
  final String? error;
  final List<WatchModel>? _watchList;
  @override
  List<WatchModel>? get watchList {
    final value = _watchList;
    if (value == null) return null;
    if (_watchList is EqualUnmodifiableListView) return _watchList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final WatchModel? watchListDetail;
  @override
  final WatchlistItemEntity? watchListByInstrument;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int totalPages;
  @override
  @JsonKey()
  final bool hasMore;
  @override
  @JsonKey()
  final bool isLoadMore;
  @override
  final SortType? sortByPriceAsc;
  @override
  final SortType? sortByChangeAsc;

  @override
  String toString() {
    return 'WatchListState(addToWatchListStatus: $addToWatchListStatus, removeFromWatchListStatus: $removeFromWatchListStatus, getWatchListStatus: $getWatchListStatus, getWatchListDetailStatus: $getWatchListDetailStatus, getWatchListByInstrumentStatus: $getWatchListByInstrumentStatus, error: $error, watchList: $watchList, watchListDetail: $watchListDetail, watchListByInstrument: $watchListByInstrument, currentPage: $currentPage, totalPages: $totalPages, hasMore: $hasMore, isLoadMore: $isLoadMore, sortByPriceAsc: $sortByPriceAsc, sortByChangeAsc: $sortByChangeAsc)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WatchListStateImpl &&
            (identical(other.addToWatchListStatus, addToWatchListStatus) ||
                other.addToWatchListStatus == addToWatchListStatus) &&
            (identical(other.removeFromWatchListStatus,
                    removeFromWatchListStatus) ||
                other.removeFromWatchListStatus == removeFromWatchListStatus) &&
            (identical(other.getWatchListStatus, getWatchListStatus) ||
                other.getWatchListStatus == getWatchListStatus) &&
            (identical(
                    other.getWatchListDetailStatus, getWatchListDetailStatus) ||
                other.getWatchListDetailStatus == getWatchListDetailStatus) &&
            (identical(other.getWatchListByInstrumentStatus,
                    getWatchListByInstrumentStatus) ||
                other.getWatchListByInstrumentStatus ==
                    getWatchListByInstrumentStatus) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality()
                .equals(other._watchList, _watchList) &&
            (identical(other.watchListDetail, watchListDetail) ||
                other.watchListDetail == watchListDetail) &&
            (identical(other.watchListByInstrument, watchListByInstrument) ||
                other.watchListByInstrument == watchListByInstrument) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.isLoadMore, isLoadMore) ||
                other.isLoadMore == isLoadMore) &&
            (identical(other.sortByPriceAsc, sortByPriceAsc) ||
                other.sortByPriceAsc == sortByPriceAsc) &&
            (identical(other.sortByChangeAsc, sortByChangeAsc) ||
                other.sortByChangeAsc == sortByChangeAsc));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      addToWatchListStatus,
      removeFromWatchListStatus,
      getWatchListStatus,
      getWatchListDetailStatus,
      getWatchListByInstrumentStatus,
      error,
      const DeepCollectionEquality().hash(_watchList),
      watchListDetail,
      watchListByInstrument,
      currentPage,
      totalPages,
      hasMore,
      isLoadMore,
      sortByPriceAsc,
      sortByChangeAsc);

  /// Create a copy of WatchListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WatchListStateImplCopyWith<_$WatchListStateImpl> get copyWith =>
      __$$WatchListStateImplCopyWithImpl<_$WatchListStateImpl>(
          this, _$identity);
}

abstract class _WatchListState implements WatchListState {
  const factory _WatchListState(
      {final DataStatus addToWatchListStatus,
      final DataStatus removeFromWatchListStatus,
      final DataStatus getWatchListStatus,
      final DataStatus getWatchListDetailStatus,
      final DataStatus getWatchListByInstrumentStatus,
      final String? error,
      final List<WatchModel>? watchList,
      final WatchModel? watchListDetail,
      final WatchlistItemEntity? watchListByInstrument,
      final int currentPage,
      final int totalPages,
      final bool hasMore,
      final bool isLoadMore,
      final SortType? sortByPriceAsc,
      final SortType? sortByChangeAsc}) = _$WatchListStateImpl;

  @override
  DataStatus get addToWatchListStatus;
  @override
  DataStatus get removeFromWatchListStatus;
  @override
  DataStatus get getWatchListStatus;
  @override
  DataStatus get getWatchListDetailStatus;
  @override
  DataStatus get getWatchListByInstrumentStatus;
  @override
  String? get error;
  @override
  List<WatchModel>? get watchList;
  @override
  WatchModel? get watchListDetail;
  @override
  WatchlistItemEntity? get watchListByInstrument;
  @override
  int get currentPage;
  @override
  int get totalPages;
  @override
  bool get hasMore;
  @override
  bool get isLoadMore;
  @override
  SortType? get sortByPriceAsc;
  @override
  SortType? get sortByChangeAsc;

  /// Create a copy of WatchListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WatchListStateImplCopyWith<_$WatchListStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
