import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/models/watch_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'watch_list_state.freezed.dart';

@freezed
class WatchListState with _$WatchListState {
  const factory WatchListState({
    @Default(DataStatus.idle) DataStatus addToWatchListStatus,
    @Default(DataStatus.idle) DataStatus removeFromWatchListStatus,
    @Default(DataStatus.idle) DataStatus getWatchListStatus,
    @Default(DataStatus.idle) DataStatus getWatchListDetailStatus,
    @Default(DataStatus.idle) DataStatus getWatchListByInstrumentStatus,
    String? error,
    List<WatchModel>? watchList,
    WatchModel? watchListDetail,
    WatchlistItemEntity? watchListByInstrument,
    @Default(1) int currentPage,
    @Default(1) int totalPages,
    @Default(true) bool hasMore,
    @Default(false) bool isLoadMore,
    SortType? sortByPriceAsc,
    SortType? sortByChangeAsc,
  }) = _WatchListState;
}
