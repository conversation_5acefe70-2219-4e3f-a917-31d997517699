import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class FTradeBuySellService {
  /// 创建期货订单
  static Future<(bool?, String?)> fetchCreateFTradeOrderResult({required Map<String, dynamic> queryParameters}) async {
    assert(queryParameters.isNotEmpty, 'queryParameters is empty');
    final response = await Http().request<bool>(
      ApiEndpoints.postFuturesCreateOrder,
      method: HttpMethod.post,
      params: queryParameters,
    );
    return (response.data, response.msg);
  }
}
