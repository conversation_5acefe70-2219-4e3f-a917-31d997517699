import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/core/utils/log.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/watch_list/domain/models/watch_model.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_state.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class WishButton extends StatelessWidget {
  const WishButton({super.key, required this.instrument});

  final String instrument;

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<WatchListCubit, WatchListState>(
          listenWhen: (previous, current) => previous.addToWatchListStatus != current.addToWatchListStatus,
          listener: (context, state) {
            if (state.addToWatchListStatus == DataStatus.success) {
              context.read<WatchListCubit>().getWatchList();
              context.read<WatchListCubit>().getWatchListByInstrument(instrument);
            } else if (state.addToWatchListStatus == DataStatus.failed) {
              GPEasyLoading.showToast(state.error ?? 'watchlist_add_error'.tr());
            }
          },
        ),
        BlocListener<WatchListCubit, WatchListState>(
          listenWhen: (previous, current) => previous.removeFromWatchListStatus != current.removeFromWatchListStatus,
          listener: (context, state) {
            if (state.removeFromWatchListStatus == DataStatus.success) {
              context.read<WatchListCubit>().getWatchList();
              context.read<WatchListCubit>().getWatchListByInstrument(instrument);
            } else if (state.removeFromWatchListStatus == DataStatus.failed) {
              GPEasyLoading.showToast(state.error ?? 'watchlist_remove_error'.tr());
            }
          },
        ),
      ],
      child: BlocSelector<WatchListCubit, WatchListState, WatchlistItemEntity?>(
        selector: (state) => state.watchListByInstrument,
        builder: (context, state) {


          final isAdded = state != null;
          return GestureDetector(
            onTap: () {
              if (isAdded) {
                context.read<WatchListCubit>().removeFromWatchList(state.id);
                return;
              }
              context.read<WatchListCubit>().addToWatchList(
                    market: instrument.split('|')[0],
                    securityType: instrument.split('|')[1],
                    symbol: instrument.split('|')[2],
                  );
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  height: 30.gh,
                  width: 30.gw,
                  padding: EdgeInsets.all(6.gr),
                  decoration: BoxDecoration(
                    color: context.theme.cardColor,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Color.fromARGB(255, 64, 54, 90).withNewOpacity(0.3)
                            : Color(0xFFD3D1D8).withNewOpacity(0.3),
                        blurRadius: 16,
                        spreadRadius: 0,
                        offset: Offset(4, 9),
                      ),
                    ],
                  ),
                  child: SvgPicture.asset(
                    isAdded ? Assets.filledHeartIcon : Assets.heartIcon,
                  ),
                ),
                Text('watchlistButton'.tr(),
                    style: context.textTheme.regular.fs12),
              ],
            ),
          );
        },
      ),
    );
  }
}
