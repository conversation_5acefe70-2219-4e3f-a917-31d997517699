// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) {
  return _NotificationModel.fromJson(json);
}

/// @nodoc
mixin _$NotificationModel {
  int? get code => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;
  NotificationData? get data => throw _privateConstructorUsedError;

  /// Serializes this NotificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationModelCopyWith<NotificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationModelCopyWith<$Res> {
  factory $NotificationModelCopyWith(
          NotificationModel value, $Res Function(NotificationModel) then) =
      _$NotificationModelCopyWithImpl<$Res, NotificationModel>;
  @useResult
  $Res call({int? code, String? msg, NotificationData? data});

  $NotificationDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$NotificationModelCopyWithImpl<$Res, $Val extends NotificationModel>
    implements $NotificationModelCopyWith<$Res> {
  _$NotificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? msg = freezed,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as NotificationData?,
    ) as $Val);
  }

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NotificationDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $NotificationDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$NotificationModelImplCopyWith<$Res>
    implements $NotificationModelCopyWith<$Res> {
  factory _$$NotificationModelImplCopyWith(_$NotificationModelImpl value,
          $Res Function(_$NotificationModelImpl) then) =
      __$$NotificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, String? msg, NotificationData? data});

  @override
  $NotificationDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$NotificationModelImplCopyWithImpl<$Res>
    extends _$NotificationModelCopyWithImpl<$Res, _$NotificationModelImpl>
    implements _$$NotificationModelImplCopyWith<$Res> {
  __$$NotificationModelImplCopyWithImpl(_$NotificationModelImpl _value,
      $Res Function(_$NotificationModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? msg = freezed,
    Object? data = freezed,
  }) {
    return _then(_$NotificationModelImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as NotificationData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationModelImpl implements _NotificationModel {
  const _$NotificationModelImpl({this.code, this.msg, this.data});

  factory _$NotificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationModelImplFromJson(json);

  @override
  final int? code;
  @override
  final String? msg;
  @override
  final NotificationData? data;

  @override
  String toString() {
    return 'NotificationModel(code: $code, msg: $msg, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationModelImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.msg, msg) || other.msg == msg) &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, msg, data);

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationModelImplCopyWith<_$NotificationModelImpl> get copyWith =>
      __$$NotificationModelImplCopyWithImpl<_$NotificationModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationModelImplToJson(
      this,
    );
  }
}

abstract class _NotificationModel implements NotificationModel {
  const factory _NotificationModel(
      {final int? code,
      final String? msg,
      final NotificationData? data}) = _$NotificationModelImpl;

  factory _NotificationModel.fromJson(Map<String, dynamic> json) =
      _$NotificationModelImpl.fromJson;

  @override
  int? get code;
  @override
  String? get msg;
  @override
  NotificationData? get data;

  /// Create a copy of NotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationModelImplCopyWith<_$NotificationModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NotificationData _$NotificationDataFromJson(Map<String, dynamic> json) {
  return _NotificationData.fromJson(json);
}

/// @nodoc
mixin _$NotificationData {
  int? get current => throw _privateConstructorUsedError;
  bool? get optimizeCountSql => throw _privateConstructorUsedError;
  List<dynamic>? get orders => throw _privateConstructorUsedError;
  int? get pages => throw _privateConstructorUsedError;
  List<NotificationRecord>? get records => throw _privateConstructorUsedError;
  bool? get searchCount => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;

  /// Serializes this NotificationData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationDataCopyWith<NotificationData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationDataCopyWith<$Res> {
  factory $NotificationDataCopyWith(
          NotificationData value, $Res Function(NotificationData) then) =
      _$NotificationDataCopyWithImpl<$Res, NotificationData>;
  @useResult
  $Res call(
      {int? current,
      bool? optimizeCountSql,
      List<dynamic>? orders,
      int? pages,
      List<NotificationRecord>? records,
      bool? searchCount,
      int? size,
      int? total});
}

/// @nodoc
class _$NotificationDataCopyWithImpl<$Res, $Val extends NotificationData>
    implements $NotificationDataCopyWith<$Res> {
  _$NotificationDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? optimizeCountSql = freezed,
    Object? orders = freezed,
    Object? pages = freezed,
    Object? records = freezed,
    Object? searchCount = freezed,
    Object? size = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      optimizeCountSql: freezed == optimizeCountSql
          ? _value.optimizeCountSql
          : optimizeCountSql // ignore: cast_nullable_to_non_nullable
              as bool?,
      orders: freezed == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      pages: freezed == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int?,
      records: freezed == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<NotificationRecord>?,
      searchCount: freezed == searchCount
          ? _value.searchCount
          : searchCount // ignore: cast_nullable_to_non_nullable
              as bool?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationDataImplCopyWith<$Res>
    implements $NotificationDataCopyWith<$Res> {
  factory _$$NotificationDataImplCopyWith(_$NotificationDataImpl value,
          $Res Function(_$NotificationDataImpl) then) =
      __$$NotificationDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? current,
      bool? optimizeCountSql,
      List<dynamic>? orders,
      int? pages,
      List<NotificationRecord>? records,
      bool? searchCount,
      int? size,
      int? total});
}

/// @nodoc
class __$$NotificationDataImplCopyWithImpl<$Res>
    extends _$NotificationDataCopyWithImpl<$Res, _$NotificationDataImpl>
    implements _$$NotificationDataImplCopyWith<$Res> {
  __$$NotificationDataImplCopyWithImpl(_$NotificationDataImpl _value,
      $Res Function(_$NotificationDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? optimizeCountSql = freezed,
    Object? orders = freezed,
    Object? pages = freezed,
    Object? records = freezed,
    Object? searchCount = freezed,
    Object? size = freezed,
    Object? total = freezed,
  }) {
    return _then(_$NotificationDataImpl(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      optimizeCountSql: freezed == optimizeCountSql
          ? _value.optimizeCountSql
          : optimizeCountSql // ignore: cast_nullable_to_non_nullable
              as bool?,
      orders: freezed == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      pages: freezed == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int?,
      records: freezed == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<NotificationRecord>?,
      searchCount: freezed == searchCount
          ? _value.searchCount
          : searchCount // ignore: cast_nullable_to_non_nullable
              as bool?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationDataImpl implements _NotificationData {
  const _$NotificationDataImpl(
      {this.current,
      this.optimizeCountSql,
      final List<dynamic>? orders,
      this.pages,
      final List<NotificationRecord>? records,
      this.searchCount,
      this.size,
      this.total})
      : _orders = orders,
        _records = records;

  factory _$NotificationDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationDataImplFromJson(json);

  @override
  final int? current;
  @override
  final bool? optimizeCountSql;
  final List<dynamic>? _orders;
  @override
  List<dynamic>? get orders {
    final value = _orders;
    if (value == null) return null;
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? pages;
  final List<NotificationRecord>? _records;
  @override
  List<NotificationRecord>? get records {
    final value = _records;
    if (value == null) return null;
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? searchCount;
  @override
  final int? size;
  @override
  final int? total;

  @override
  String toString() {
    return 'NotificationData(current: $current, optimizeCountSql: $optimizeCountSql, orders: $orders, pages: $pages, records: $records, searchCount: $searchCount, size: $size, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationDataImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.optimizeCountSql, optimizeCountSql) ||
                other.optimizeCountSql == optimizeCountSql) &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.searchCount, searchCount) ||
                other.searchCount == searchCount) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      current,
      optimizeCountSql,
      const DeepCollectionEquality().hash(_orders),
      pages,
      const DeepCollectionEquality().hash(_records),
      searchCount,
      size,
      total);

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationDataImplCopyWith<_$NotificationDataImpl> get copyWith =>
      __$$NotificationDataImplCopyWithImpl<_$NotificationDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationDataImplToJson(
      this,
    );
  }
}

abstract class _NotificationData implements NotificationData {
  const factory _NotificationData(
      {final int? current,
      final bool? optimizeCountSql,
      final List<dynamic>? orders,
      final int? pages,
      final List<NotificationRecord>? records,
      final bool? searchCount,
      final int? size,
      final int? total}) = _$NotificationDataImpl;

  factory _NotificationData.fromJson(Map<String, dynamic> json) =
      _$NotificationDataImpl.fromJson;

  @override
  int? get current;
  @override
  bool? get optimizeCountSql;
  @override
  List<dynamic>? get orders;
  @override
  int? get pages;
  @override
  List<NotificationRecord>? get records;
  @override
  bool? get searchCount;
  @override
  int? get size;
  @override
  int? get total;

  /// Create a copy of NotificationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationDataImplCopyWith<_$NotificationDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NotificationRecord _$NotificationRecordFromJson(Map<String, dynamic> json) {
  return _NotificationRecord.fromJson(json);
}

/// @nodoc
mixin _$NotificationRecord {
  String? get content => throw _privateConstructorUsedError;
  int? get contentType => throw _privateConstructorUsedError;
  String? get extra => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  int? get isNeedPush => throw _privateConstructorUsedError;
  String? get messageNo => throw _privateConstructorUsedError;
  int? get messageType => throw _privateConstructorUsedError;
  int? get pushStatus => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;

  /// Serializes this NotificationRecord to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationRecordCopyWith<NotificationRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationRecordCopyWith<$Res> {
  factory $NotificationRecordCopyWith(
          NotificationRecord value, $Res Function(NotificationRecord) then) =
      _$NotificationRecordCopyWithImpl<$Res, NotificationRecord>;
  @useResult
  $Res call(
      {String? content,
      int? contentType,
      String? extra,
      int? id,
      int? isNeedPush,
      String? messageNo,
      int? messageType,
      int? pushStatus,
      int? status,
      String? title,
      String? type,
      int? userId,
      String? createTime});
}

/// @nodoc
class _$NotificationRecordCopyWithImpl<$Res, $Val extends NotificationRecord>
    implements $NotificationRecordCopyWith<$Res> {
  _$NotificationRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? contentType = freezed,
    Object? extra = freezed,
    Object? id = freezed,
    Object? isNeedPush = freezed,
    Object? messageNo = freezed,
    Object? messageType = freezed,
    Object? pushStatus = freezed,
    Object? status = freezed,
    Object? title = freezed,
    Object? type = freezed,
    Object? userId = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_value.copyWith(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      contentType: freezed == contentType
          ? _value.contentType
          : contentType // ignore: cast_nullable_to_non_nullable
              as int?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      isNeedPush: freezed == isNeedPush
          ? _value.isNeedPush
          : isNeedPush // ignore: cast_nullable_to_non_nullable
              as int?,
      messageNo: freezed == messageNo
          ? _value.messageNo
          : messageNo // ignore: cast_nullable_to_non_nullable
              as String?,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as int?,
      pushStatus: freezed == pushStatus
          ? _value.pushStatus
          : pushStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationRecordImplCopyWith<$Res>
    implements $NotificationRecordCopyWith<$Res> {
  factory _$$NotificationRecordImplCopyWith(_$NotificationRecordImpl value,
          $Res Function(_$NotificationRecordImpl) then) =
      __$$NotificationRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? content,
      int? contentType,
      String? extra,
      int? id,
      int? isNeedPush,
      String? messageNo,
      int? messageType,
      int? pushStatus,
      int? status,
      String? title,
      String? type,
      int? userId,
      String? createTime});
}

/// @nodoc
class __$$NotificationRecordImplCopyWithImpl<$Res>
    extends _$NotificationRecordCopyWithImpl<$Res, _$NotificationRecordImpl>
    implements _$$NotificationRecordImplCopyWith<$Res> {
  __$$NotificationRecordImplCopyWithImpl(_$NotificationRecordImpl _value,
      $Res Function(_$NotificationRecordImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? contentType = freezed,
    Object? extra = freezed,
    Object? id = freezed,
    Object? isNeedPush = freezed,
    Object? messageNo = freezed,
    Object? messageType = freezed,
    Object? pushStatus = freezed,
    Object? status = freezed,
    Object? title = freezed,
    Object? type = freezed,
    Object? userId = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_$NotificationRecordImpl(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      contentType: freezed == contentType
          ? _value.contentType
          : contentType // ignore: cast_nullable_to_non_nullable
              as int?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      isNeedPush: freezed == isNeedPush
          ? _value.isNeedPush
          : isNeedPush // ignore: cast_nullable_to_non_nullable
              as int?,
      messageNo: freezed == messageNo
          ? _value.messageNo
          : messageNo // ignore: cast_nullable_to_non_nullable
              as String?,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as int?,
      pushStatus: freezed == pushStatus
          ? _value.pushStatus
          : pushStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationRecordImpl implements _NotificationRecord {
  const _$NotificationRecordImpl(
      {this.content,
      this.contentType,
      this.extra,
      this.id,
      this.isNeedPush,
      this.messageNo,
      this.messageType,
      this.pushStatus,
      this.status,
      this.title,
      this.type,
      this.userId,
      this.createTime});

  factory _$NotificationRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationRecordImplFromJson(json);

  @override
  final String? content;
  @override
  final int? contentType;
  @override
  final String? extra;
  @override
  final int? id;
  @override
  final int? isNeedPush;
  @override
  final String? messageNo;
  @override
  final int? messageType;
  @override
  final int? pushStatus;
  @override
  final int? status;
  @override
  final String? title;
  @override
  final String? type;
  @override
  final int? userId;
  @override
  final String? createTime;

  @override
  String toString() {
    return 'NotificationRecord(content: $content, contentType: $contentType, extra: $extra, id: $id, isNeedPush: $isNeedPush, messageNo: $messageNo, messageType: $messageType, pushStatus: $pushStatus, status: $status, title: $title, type: $type, userId: $userId, createTime: $createTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationRecordImpl &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.extra, extra) || other.extra == extra) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isNeedPush, isNeedPush) ||
                other.isNeedPush == isNeedPush) &&
            (identical(other.messageNo, messageNo) ||
                other.messageNo == messageNo) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.pushStatus, pushStatus) ||
                other.pushStatus == pushStatus) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      content,
      contentType,
      extra,
      id,
      isNeedPush,
      messageNo,
      messageType,
      pushStatus,
      status,
      title,
      type,
      userId,
      createTime);

  /// Create a copy of NotificationRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationRecordImplCopyWith<_$NotificationRecordImpl> get copyWith =>
      __$$NotificationRecordImplCopyWithImpl<_$NotificationRecordImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationRecordImplToJson(
      this,
    );
  }
}

abstract class _NotificationRecord implements NotificationRecord {
  const factory _NotificationRecord(
      {final String? content,
      final int? contentType,
      final String? extra,
      final int? id,
      final int? isNeedPush,
      final String? messageNo,
      final int? messageType,
      final int? pushStatus,
      final int? status,
      final String? title,
      final String? type,
      final int? userId,
      final String? createTime}) = _$NotificationRecordImpl;

  factory _NotificationRecord.fromJson(Map<String, dynamic> json) =
      _$NotificationRecordImpl.fromJson;

  @override
  String? get content;
  @override
  int? get contentType;
  @override
  String? get extra;
  @override
  int? get id;
  @override
  int? get isNeedPush;
  @override
  String? get messageNo;
  @override
  int? get messageType;
  @override
  int? get pushStatus;
  @override
  int? get status;
  @override
  String? get title;
  @override
  String? get type;
  @override
  int? get userId;
  @override
  String? get createTime;

  /// Create a copy of NotificationRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationRecordImplCopyWith<_$NotificationRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
