import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

@freezed
class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    int? code,
    String? msg,
    NotificationData? data,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) => _$NotificationModelFromJson(json);
}

@freezed
class NotificationData with _$NotificationData {
  const factory NotificationData({
    int? current,
    bool? optimizeCountSql,
    List<dynamic>? orders,
    int? pages,
    List<NotificationRecord>? records,
    bool? searchCount,
    int? size,
    int? total,
  }) = _NotificationData;

  factory NotificationData.fromJson(Map<String, dynamic> json) => _$NotificationDataFromJson(json);
}

@freezed
class NotificationRecord with _$NotificationRecord {
  const factory NotificationRecord({
    String? content,
    int? contentType,
    String? extra,
    int? id,
    int? isNeedPush,
    String? messageNo,
    int? messageType,
    int? pushStatus,
    int? status,
    String? title,
    String? type,
    int? userId,
    String? createTime,
  }) = _NotificationRecord;

  factory NotificationRecord.fromJson(Map<String, dynamic> json) => _$NotificationRecordFromJson(json);
}
