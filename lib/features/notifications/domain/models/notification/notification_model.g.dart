// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationModelImpl _$$NotificationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationModelImpl(
      code: (json['code'] as num?)?.toInt(),
      msg: json['msg'] as String?,
      data: json['data'] == null
          ? null
          : NotificationData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$NotificationModelImplToJson(
        _$NotificationModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

_$NotificationDataImpl _$$NotificationDataImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationDataImpl(
      current: (json['current'] as num?)?.toInt(),
      optimizeCountSql: json['optimizeCountSql'] as bool?,
      orders: json['orders'] as List<dynamic>?,
      pages: (json['pages'] as num?)?.toInt(),
      records: (json['records'] as List<dynamic>?)
          ?.map((e) => NotificationRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
      searchCount: json['searchCount'] as bool?,
      size: (json['size'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$NotificationDataImplToJson(
        _$NotificationDataImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'optimizeCountSql': instance.optimizeCountSql,
      'orders': instance.orders,
      'pages': instance.pages,
      'records': instance.records,
      'searchCount': instance.searchCount,
      'size': instance.size,
      'total': instance.total,
    };

_$NotificationRecordImpl _$$NotificationRecordImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationRecordImpl(
      content: json['content'] as String?,
      contentType: (json['contentType'] as num?)?.toInt(),
      extra: json['extra'] as String?,
      id: (json['id'] as num?)?.toInt(),
      isNeedPush: (json['isNeedPush'] as num?)?.toInt(),
      messageNo: json['messageNo'] as String?,
      messageType: (json['messageType'] as num?)?.toInt(),
      pushStatus: (json['pushStatus'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      title: json['title'] as String?,
      type: json['type'] as String?,
      userId: (json['userId'] as num?)?.toInt(),
      createTime: json['createTime'] as String?,
    );

Map<String, dynamic> _$$NotificationRecordImplToJson(
        _$NotificationRecordImpl instance) =>
    <String, dynamic>{
      'content': instance.content,
      'contentType': instance.contentType,
      'extra': instance.extra,
      'id': instance.id,
      'isNeedPush': instance.isNeedPush,
      'messageNo': instance.messageNo,
      'messageType': instance.messageType,
      'pushStatus': instance.pushStatus,
      'status': instance.status,
      'title': instance.title,
      'type': instance.type,
      'userId': instance.userId,
      'createTime': instance.createTime,
    };
