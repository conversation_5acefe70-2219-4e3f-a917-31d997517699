import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/statement_entity.dart';

StatementEntity $StatementEntityFromJson(Map<String, dynamic> json) {
  final StatementEntity statementEntity = StatementEntity();
  final List<StatementRecords>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<StatementRecords>(e) as StatementRecords)
      .toList();
  if (records != null) {
    statementEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    statementEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    statementEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    statementEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    statementEntity.pages = pages;
  }
  return statementEntity;
}

Map<String, dynamic> $StatementEntityToJson(StatementEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension StatementEntityExtension on StatementEntity {
  StatementEntity copyWith({
    List<StatementRecords>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return StatementEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

StatementRecords $StatementRecordsFromJson(Map<String, dynamic> json) {
  final StatementRecords statementRecords = StatementRecords();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    statementRecords.id = id;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    statementRecords.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    statementRecords.userNo = userNo;
  }
  final int? agentId = jsonConvert.convert<int>(json['agentId']);
  if (agentId != null) {
    statementRecords.agentId = agentId;
  }
  final int? generalAgentId = jsonConvert.convert<int>(json['generalAgentId']);
  if (generalAgentId != null) {
    statementRecords.generalAgentId = generalAgentId;
  }
  final String? fundChangeWayCode = jsonConvert.convert<String>(
      json['fundChangeWayCode']);
  if (fundChangeWayCode != null) {
    statementRecords.fundChangeWayCode = fundChangeWayCode;
  }
  final String? fundChangeWayName = jsonConvert.convert<String>(
      json['fundChangeWayName']);
  if (fundChangeWayName != null) {
    statementRecords.fundChangeWayName = fundChangeWayName;
  }
  final String? fundChangeTypeCode = jsonConvert.convert<String>(
      json['fundChangeTypeCode']);
  if (fundChangeTypeCode != null) {
    statementRecords.fundChangeTypeCode = fundChangeTypeCode;
  }
  final String? fundChangeTypeName = jsonConvert.convert<String>(
      json['fundChangeTypeName']);
  if (fundChangeTypeName != null) {
    statementRecords.fundChangeTypeName = fundChangeTypeName;
  }
  final double? beforeAccountAmount = jsonConvert.convert<double>(
      json['beforeAccountAmount']);
  if (beforeAccountAmount != null) {
    statementRecords.beforeAccountAmount = beforeAccountAmount;
  }
  final double? changeAmount = jsonConvert.convert<double>(
      json['changeAmount']);
  if (changeAmount != null) {
    statementRecords.changeAmount = changeAmount;
  }
  final double? afterAccountAmount = jsonConvert.convert<double>(
      json['afterAccountAmount']);
  if (afterAccountAmount != null) {
    statementRecords.afterAccountAmount = afterAccountAmount;
  }
  final dynamic transactionNo = json['transactionNo'];
  if (transactionNo != null) {
    statementRecords.transactionNo = transactionNo;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    statementRecords.remark = remark;
  }
  final dynamic operator = json['operator'];
  if (operator != null) {
    statementRecords.operator = operator;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    statementRecords.createTime = createTime;
  }
  final dynamic currencyCode = json['currencyCode'];
  if (currencyCode != null) {
    statementRecords.currencyCode = currencyCode;
  }
  final int? platformId = jsonConvert.convert<int>(json['platformId']);
  if (platformId != null) {
    statementRecords.platformId = platformId;
  }
  final dynamic lotteryId = json['lotteryId'];
  if (lotteryId != null) {
    statementRecords.lotteryId = lotteryId;
  }
  final dynamic lotteryName = json['lotteryName'];
  if (lotteryName != null) {
    statementRecords.lotteryName = lotteryName;
  }
  final dynamic belongDate = json['belongDate'];
  if (belongDate != null) {
    statementRecords.belongDate = belongDate;
  }
  final dynamic createTimeReport = json['createTimeReport'];
  if (createTimeReport != null) {
    statementRecords.createTimeReport = createTimeReport;
  }
  return statementRecords;
}

Map<String, dynamic> $StatementRecordsToJson(StatementRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['agentId'] = entity.agentId;
  data['generalAgentId'] = entity.generalAgentId;
  data['fundChangeWayCode'] = entity.fundChangeWayCode;
  data['fundChangeWayName'] = entity.fundChangeWayName;
  data['fundChangeTypeCode'] = entity.fundChangeTypeCode;
  data['fundChangeTypeName'] = entity.fundChangeTypeName;
  data['beforeAccountAmount'] = entity.beforeAccountAmount;
  data['changeAmount'] = entity.changeAmount;
  data['afterAccountAmount'] = entity.afterAccountAmount;
  data['transactionNo'] = entity.transactionNo;
  data['remark'] = entity.remark;
  data['operator'] = entity.operator;
  data['createTime'] = entity.createTime;
  data['currencyCode'] = entity.currencyCode;
  data['platformId'] = entity.platformId;
  data['lotteryId'] = entity.lotteryId;
  data['lotteryName'] = entity.lotteryName;
  data['belongDate'] = entity.belongDate;
  data['createTimeReport'] = entity.createTimeReport;
  return data;
}

extension StatementRecordsExtension on StatementRecords {
  StatementRecords copyWith({
    String? id,
    int? userId,
    String? userNo,
    int? agentId,
    int? generalAgentId,
    String? fundChangeWayCode,
    String? fundChangeWayName,
    String? fundChangeTypeCode,
    String? fundChangeTypeName,
    double? beforeAccountAmount,
    double? changeAmount,
    double? afterAccountAmount,
    dynamic transactionNo,
    String? remark,
    dynamic operator,
    int? createTime,
    dynamic currencyCode,
    int? platformId,
    dynamic lotteryId,
    dynamic lotteryName,
    dynamic belongDate,
    dynamic createTimeReport,
  }) {
    return StatementRecords()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..agentId = agentId ?? this.agentId
      ..generalAgentId = generalAgentId ?? this.generalAgentId
      ..fundChangeWayCode = fundChangeWayCode ?? this.fundChangeWayCode
      ..fundChangeWayName = fundChangeWayName ?? this.fundChangeWayName
      ..fundChangeTypeCode = fundChangeTypeCode ?? this.fundChangeTypeCode
      ..fundChangeTypeName = fundChangeTypeName ?? this.fundChangeTypeName
      ..beforeAccountAmount = beforeAccountAmount ?? this.beforeAccountAmount
      ..changeAmount = changeAmount ?? this.changeAmount
      ..afterAccountAmount = afterAccountAmount ?? this.afterAccountAmount
      ..transactionNo = transactionNo ?? this.transactionNo
      ..remark = remark ?? this.remark
      ..operator = operator ?? this.operator
      ..createTime = createTime ?? this.createTime
      ..currencyCode = currencyCode ?? this.currencyCode
      ..platformId = platformId ?? this.platformId
      ..lotteryId = lotteryId ?? this.lotteryId
      ..lotteryName = lotteryName ?? this.lotteryName
      ..belongDate = belongDate ?? this.belongDate
      ..createTimeReport = createTimeReport ?? this.createTimeReport;
  }
}