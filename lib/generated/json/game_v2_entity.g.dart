import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:equatable/equatable.dart';

import 'package:easy_localization/easy_localization.dart';


GameTypeV2ListEntity $GameTypeV2ListEntityFromJson(Map<String, dynamic> json) {
  final GameTypeV2ListEntity gameTypeV2ListEntity = GameTypeV2ListEntity();
  final List<GameTypeV2>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GameTypeV2>(e) as GameTypeV2).toList();
  if (list != null) {
    gameTypeV2ListEntity.list = list;
  }
  return gameTypeV2ListEntity;
}

Map<String, dynamic> $GameTypeV2ListEntityToJson(GameTypeV2ListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension GameTypeV2ListEntityExtension on GameTypeV2ListEntity {
  GameTypeV2ListEntity copyWith({
    List<GameTypeV2>? list,
  }) {
    return GameTypeV2ListEntity()
      ..list = list ?? this.list;
  }
}

GameTypeV2 $GameTypeV2FromJson(Map<String, dynamic> json) {
  final GameTypeV2 gameTypeV2 = GameTypeV2();
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    gameTypeV2.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    gameTypeV2.name = name;
  }
  final List<GamePlatformV2>? data = (json['plats'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<GamePlatformV2>(e) as GamePlatformV2)
      .toList();
  if (data != null) {
    gameTypeV2.data = data;
  }
  final List<GameV2>? games = (json['games'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GameV2>(e) as GameV2).toList();
  if (games != null) {
    gameTypeV2.games = games;
  }
  final String? classIcon = jsonConvert.convert<String>(json['classIcon']);
  if (classIcon != null) {
    gameTypeV2.classIcon = classIcon;
  }
  final int? type = jsonConvert.convert<int>(json['gameType']);
  if (type != null) {
    gameTypeV2.type = type;
  }
  final int? isGame = jsonConvert.convert<int>(json['isGame']);
  if (isGame != null) {
    gameTypeV2.isGame = isGame;
  }
  final double? sectionHeight = jsonConvert.convert<double>(
      json['sectionHeight']);
  if (sectionHeight != null) {
    gameTypeV2.sectionHeight = sectionHeight;
  }
  return gameTypeV2;
}

Map<String, dynamic> $GameTypeV2ToJson(GameTypeV2 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['plats'] = entity.data.map((v) => v.toJson()).toList();
  data['games'] = entity.games.map((v) => v.toJson()).toList();
  data['classIcon'] = entity.classIcon;
  data['gameType'] = entity.type;
  data['isGame'] = entity.isGame;
  data['sectionHeight'] = entity.sectionHeight;
  return data;
}

extension GameTypeV2Extension on GameTypeV2 {
  GameTypeV2 copyWith({
    String? code,
    String? name,
    List<GamePlatformV2>? data,
    List<GameV2>? games,
    String? classIcon,
    int? type,
    int? isGame,
    double? sectionHeight,
  }) {
    return GameTypeV2()
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..data = data ?? this.data
      ..games = games ?? this.games
      ..classIcon = classIcon ?? this.classIcon
      ..type = type ?? this.type
      ..isGame = isGame ?? this.isGame
      ..sectionHeight = sectionHeight ?? this.sectionHeight;
  }
}

GamePlatformV2 $GamePlatformV2FromJson(Map<String, dynamic> json) {
  final GamePlatformV2 gamePlatformV2 = GamePlatformV2();
  final int? id = jsonConvert.convert<int>(json['platformId']);
  if (id != null) {
    gamePlatformV2.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['platformName']);
  if (name != null) {
    gamePlatformV2.name = name;
  }
  final String? code = jsonConvert.convert<String>(json['platformCode']);
  if (code != null) {
    gamePlatformV2.code = code;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    gamePlatformV2.gameClassCode = gameClassCode;
  }
  final String? logoUrl = jsonConvert.convert<String>(json['icon']);
  if (logoUrl != null) {
    gamePlatformV2.logoUrl = logoUrl;
  }
  final String? mainImgUrl = jsonConvert.convert<String>(json['picture']);
  if (mainImgUrl != null) {
    gamePlatformV2.mainImgUrl = mainImgUrl;
  }
  final int? isGame = jsonConvert.convert<int>(json['isGame']);
  if (isGame != null) {
    gamePlatformV2.isGame = isGame;
  }
  final int? isAegis = jsonConvert.convert<int>(json['isAegis']);
  if (isAegis != null) {
    gamePlatformV2.isAegis = isAegis;
  }
  final int? type = jsonConvert.convert<int>(json['gameType']);
  if (type != null) {
    gamePlatformV2.type = type;
  }
  return gamePlatformV2;
}

Map<String, dynamic> $GamePlatformV2ToJson(GamePlatformV2 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['platformId'] = entity.id;
  data['platformName'] = entity.name;
  data['platformCode'] = entity.code;
  data['gameClassCode'] = entity.gameClassCode;
  data['icon'] = entity.logoUrl;
  data['picture'] = entity.mainImgUrl;
  data['isGame'] = entity.isGame;
  data['isAegis'] = entity.isAegis;
  data['gameType'] = entity.type;
  return data;
}

extension GamePlatformV2Extension on GamePlatformV2 {
  GamePlatformV2 copyWith({
    int? id,
    String? name,
    String? code,
    String? gameClassCode,
    String? logoUrl,
    String? mainImgUrl,
    int? isGame,
    int? isAegis,
    int? type,
  }) {
    return GamePlatformV2()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..code = code ?? this.code
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..logoUrl = logoUrl ?? this.logoUrl
      ..mainImgUrl = mainImgUrl ?? this.mainImgUrl
      ..isGame = isGame ?? this.isGame
      ..isAegis = isAegis ?? this.isAegis
      ..type = type ?? this.type;
  }
}

GameV2ListEntity $GameV2ListEntityFromJson(Map<String, dynamic> json) {
  final GameV2ListEntity gameV2ListEntity = GameV2ListEntity();
  final List<GameV2>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GameV2>(e) as GameV2).toList();
  if (list != null) {
    gameV2ListEntity.list = list;
  }
  return gameV2ListEntity;
}

Map<String, dynamic> $GameV2ListEntityToJson(GameV2ListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension GameV2ListEntityExtension on GameV2ListEntity {
  GameV2ListEntity copyWith({
    List<GameV2>? list,
  }) {
    return GameV2ListEntity()
      ..list = list ?? this.list;
  }
}

GameV2 $GameV2FromJson(Map<String, dynamic> json) {
  final GameV2 gameV2 = GameV2();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    gameV2.id = id;
  }
  final int? platformId = jsonConvert.convert<int>(json['platformId']);
  if (platformId != null) {
    gameV2.platformId = platformId;
  }
  final bool? isSavour = jsonConvert.convert<bool>(json['isSavour']);
  if (isSavour != null) {
    gameV2.isSavour = isSavour;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    gameV2.gameClassCode = gameClassCode;
  }
  final String? code = jsonConvert.convert<String>(json['gameCode']);
  if (code != null) {
    gameV2.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['gameName']);
  if (name != null) {
    gameV2.name = name;
  }
  final String? mainImgUrl = jsonConvert.convert<String>(json['iconUrl']);
  if (mainImgUrl != null) {
    gameV2.mainImgUrl = mainImgUrl;
  }
  final int? type = jsonConvert.convert<int>(json['gameType']);
  if (type != null) {
    gameV2.type = type;
  }
  final String? platIcon = jsonConvert.convert<String>(json['platIcon']);
  if (platIcon != null) {
    gameV2.platIcon = platIcon;
  }
  return gameV2;
}

Map<String, dynamic> $GameV2ToJson(GameV2 entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['platformId'] = entity.platformId;
  data['isSavour'] = entity.isSavour;
  data['gameClassCode'] = entity.gameClassCode;
  data['gameCode'] = entity.code;
  data['gameName'] = entity.name;
  data['iconUrl'] = entity.mainImgUrl;
  data['gameType'] = entity.type;
  data['platIcon'] = entity.platIcon;
  return data;
}

extension GameV2Extension on GameV2 {
  GameV2 copyWith({
    int? id,
    int? platformId,
    bool? isSavour,
    String? gameClassCode,
    String? code,
    String? name,
    String? mainImgUrl,
    int? type,
    String? platIcon,
  }) {
    return GameV2()
      ..id = id ?? this.id
      ..platformId = platformId ?? this.platformId
      ..isSavour = isSavour ?? this.isSavour
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..mainImgUrl = mainImgUrl ?? this.mainImgUrl
      ..type = type ?? this.type
      ..platIcon = platIcon ?? this.platIcon;
  }
}