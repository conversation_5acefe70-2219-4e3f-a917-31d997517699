import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/video_filter_entity.dart';
import 'package:easy_localization/easy_localization.dart';


VideoFilterEntityList $VideoFilterEntityListFromJson(
    Map<String, dynamic> json) {
  final VideoFilterEntityList videoFilterEntityList = VideoFilterEntityList();
  final List<VideoFilterEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<VideoFilterEntity>(e) as VideoFilterEntity)
      .toList();
  if (list != null) {
    videoFilterEntityList.list = list;
  }
  return videoFilterEntityList;
}

Map<String, dynamic> $VideoFilterEntityListToJson(
    VideoFilterEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension VideoFilterEntityListExtension on VideoFilterEntityList {
  VideoFilterEntityList copyWith({
    List<VideoFilterEntity>? list,
  }) {
    return VideoFilterEntityList()
      ..list = list ?? this.list;
  }
}

VideoFilterEntity $VideoFilterEntityFromJson(Map<String, dynamic> json) {
  final VideoFilterEntity videoFilterEntity = VideoFilterEntity();
  final String? videoCategory = jsonConvert.convert<String>(
      json['videoCategory']);
  if (videoCategory != null) {
    videoFilterEntity.videoCategory = videoCategory;
  }
  final String? videoCategoryId = jsonConvert.convert<String>(
      json['videoCategoryId']);
  if (videoCategoryId != null) {
    videoFilterEntity.videoCategoryId = videoCategoryId;
  }
  return videoFilterEntity;
}

Map<String, dynamic> $VideoFilterEntityToJson(VideoFilterEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['videoCategory'] = entity.videoCategory;
  data['videoCategoryId'] = entity.videoCategoryId;
  return data;
}

extension VideoFilterEntityExtension on VideoFilterEntity {
  VideoFilterEntity copyWith({
    String? videoCategory,
    String? videoCategoryId,
  }) {
    return VideoFilterEntity()
      ..videoCategory = videoCategory ?? this.videoCategory
      ..videoCategoryId = videoCategoryId ?? this.videoCategoryId;
  }
}