import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/activity_task_record_entity.dart';
import 'package:easy_localization/easy_localization.dart';


ActivityTaskRecordEntity $ActivityTaskRecordEntityFromJson(
    Map<String, dynamic> json) {
  final ActivityTaskRecordEntity activityTaskRecordEntity = ActivityTaskRecordEntity();
  final List<ActivityTaskRecord>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ActivityTaskRecord>(e) as ActivityTaskRecord)
      .toList();
  if (records != null) {
    activityTaskRecordEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    activityTaskRecordEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    activityTaskRecordEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    activityTaskRecordEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    activityTaskRecordEntity.pages = pages;
  }
  return activityTaskRecordEntity;
}

Map<String, dynamic> $ActivityTaskRecordEntityToJson(
    ActivityTaskRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension ActivityTaskRecordEntityExtension on ActivityTaskRecordEntity {
  ActivityTaskRecordEntity copyWith({
    List<ActivityTaskRecord>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return ActivityTaskRecordEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

ActivityTaskRecord $ActivityTaskRecordFromJson(Map<String, dynamic> json) {
  final ActivityTaskRecord activityTaskRecord = ActivityTaskRecord();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    activityTaskRecord.id = id;
  }
  final String? activeName = jsonConvert.convert<String>(json['activeName']);
  if (activeName != null) {
    activityTaskRecord.activeName = activeName;
  }
  final int? activeType = jsonConvert.convert<int>(json['activeType']);
  if (activeType != null) {
    activityTaskRecord.activeType = activeType;
  }
  final double? sendAmount = jsonConvert.convert<double>(json['sendAmount']);
  if (sendAmount != null) {
    activityTaskRecord.sendAmount = sendAmount;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    activityTaskRecord.createTime = createTime;
  }
  return activityTaskRecord;
}

Map<String, dynamic> $ActivityTaskRecordToJson(ActivityTaskRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['activeName'] = entity.activeName;
  data['activeType'] = entity.activeType;
  data['sendAmount'] = entity.sendAmount;
  data['createTime'] = entity.createTime;
  return data;
}

extension ActivityTaskRecordExtension on ActivityTaskRecord {
  ActivityTaskRecord copyWith({
    int? id,
    String? activeName,
    int? activeType,
    double? sendAmount,
    int? createTime,
  }) {
    return ActivityTaskRecord()
      ..id = id ?? this.id
      ..activeName = activeName ?? this.activeName
      ..activeType = activeType ?? this.activeType
      ..sendAmount = sendAmount ?? this.sendAmount
      ..createTime = createTime ?? this.createTime;
  }
}