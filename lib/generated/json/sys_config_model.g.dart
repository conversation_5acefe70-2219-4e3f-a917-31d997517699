import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/shared/models/sys_settings_model/sys_config_model.dart';

SysConfigModel $SysConfigModelFromJson(Map<String, dynamic> json) {
  final SysConfigModel sysConfigModel = SysConfigModel();
  final int? tradingMode = jsonConvert.convert<int>(json['tradingMode']);
  if (tradingMode != null) {
    sysConfigModel.tradingMode = tradingMode;
  }
  return sysConfigModel;
}

Map<String, dynamic> $SysConfigModelToJson(SysConfigModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['tradingMode'] = entity.tradingMode;
  return data;
}

extension SysConfigModelExtension on SysConfigModel {
  SysConfigModel copyWith({
    int? tradingMode,
  }) {
    return SysConfigModel()
      ..tradingMode = tradingMode ?? this.tradingMode;
  }
}