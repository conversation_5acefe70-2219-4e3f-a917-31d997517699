import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';

WatchlistItemEntity $WatchlistItemEntityFromJson(Map<String, dynamic> json) {
  final WatchlistItemEntity watchlistItemEntity = WatchlistItemEntity();
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    watchlistItemEntity.createTime = createTime;
  }
  final double? gain = jsonConvert.convert<double>(json['gain']);
  if (gain != null) {
    watchlistItemEntity.gain = gain;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    watchlistItemEntity.id = id;
  }
  final String? instrument = jsonConvert.convert<String>(json['instrument']);
  if (instrument != null) {
    watchlistItemEntity.instrument = instrument;
  }
  final double? latestPrice = jsonConvert.convert<double>(json['latestPrice']);
  if (latestPrice != null) {
    watchlistItemEntity.latestPrice = latestPrice;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    watchlistItemEntity.market = market;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    watchlistItemEntity.name = name;
  }
  final int? putSort = jsonConvert.convert<int>(json['putSort']);
  if (putSort != null) {
    watchlistItemEntity.putSort = putSort;
  }
  final String? securityType = jsonConvert.convert<String>(json['securityType']);
  if (securityType != null) {
    watchlistItemEntity.securityType = securityType;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    watchlistItemEntity.sort = sort;
  }
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    watchlistItemEntity.symbol = symbol;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    watchlistItemEntity.updateTime = updateTime;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    watchlistItemEntity.userId = userId;
  }
  return watchlistItemEntity;
}

Map<String, dynamic> $WatchlistItemEntityToJson(WatchlistItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['createTime'] = entity.createTime;
  data['gain'] = entity.gain;
  data['id'] = entity.id;
  data['instrument'] = entity.instrument;
  data['latestPrice'] = entity.latestPrice;
  data['market'] = entity.market;
  data['name'] = entity.name;
  data['putSort'] = entity.putSort;
  data['securityType'] = entity.securityType;
  data['sort'] = entity.sort;
  data['symbol'] = entity.symbol;
  data['updateTime'] = entity.updateTime;
  data['userId'] = entity.userId;
  return data;
}

extension WatchlistItemEntityExtension on WatchlistItemEntity {
  WatchlistItemEntity copyWith({
    String? createTime,
    double? gain,
    int? id,
    String? instrument,
    double? latestPrice,
    String? market,
    String? name,
    int? putSort,
    String? securityType,
    int? sort,
    String? symbol,
    String? updateTime,
    int? userId,
  }) {
    return WatchlistItemEntity()
      ..createTime = createTime ?? this.createTime
      ..gain = gain ?? this.gain
      ..id = id ?? this.id
      ..instrument = instrument ?? this.instrument
      ..latestPrice = latestPrice ?? this.latestPrice
      ..market = market ?? this.market
      ..name = name ?? this.name
      ..putSort = putSort ?? this.putSort
      ..securityType = securityType ?? this.securityType
      ..sort = sort ?? this.sort
      ..symbol = symbol ?? this.symbol
      ..updateTime = updateTime ?? this.updateTime
      ..userId = userId ?? this.userId;
  }
}