import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';

class GSDateTabBarWidget extends StatelessWidget {
  final TabController _tabController;
  final List<Widget> children;

  const GSDateTabBarWidget({super.key, required TabController tabController, required this.children})
      : _tabController = tabController;

  @override
  Widget build(BuildContext context) {
    return CommonCardPage(
      child: Column(
        children: [
          Container(
            height: 33.gw,
            margin: EdgeInsets.symmetric(horizontal: 15.gw),
            decoration: BoxDecoration(
              color: Theme.of(context).dividerColor,
              borderRadius: BorderRadius.circular(20.gw),
            ),
            child: CommonTabBar.withA<PERSON><PERSON>ey(
              [
                CommonTabBarItem(
                  title: 'today'.tr(),
                ),
                CommonTabBarItem(
                  title: 'yesterday'.tr(),
                ),
                CommonTabBarItem(
                  title: 'last_7_days'.tr(),
                ),
              ],
              currentIndex: _tabController.index,
              onTap: (index) {
                _tabController.animateTo(index);
              },
              style: CommonTabBarStyle.primary,
              isScrollable: false,
            ),
          ),
          SizedBox(height: 10.gw),
          Expanded(
            child: Column(
              children: children,
            ),
          ),
        ],
      ),
    );
  }
}
