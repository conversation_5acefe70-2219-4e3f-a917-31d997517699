import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/vip_model_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/vip/vip_level_cell.dart';

class VipLevelSlider extends StatelessWidget {
  final int current;
  final List<VipModel> dataList;
  final ValueChanged<int>? onIndexChanged;

  const VipLevelSlider({
    super.key,
    required this.current,
    required this.dataList,
    this.onIndexChanged,
  });

  @override
  Widget build(BuildContext context) {
    return CarouselSlider(
      options: CarouselOptions(
        initialPage: current,
        height: 200.gw,
        viewportFraction: 0.8,
        autoPlay: false,
        onPageChanged: (index, reason) {
          onIndexChanged?.call(index);
        },
      ),
      items: dataList.asMap().entries.map((entry) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          height: 200.gw,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.gw),
          ),
          child: VipLevelCell(
            index: entry.key,
            model: entry.value,
            totalLevel: dataList.length,
            isCurrentLevel: entry.value.vipLevel <= (sl<UserCubit>().state.vipInfo?.vipLevel ?? 0),
          ),
        );
      }).toList(),
    );
  }
}
