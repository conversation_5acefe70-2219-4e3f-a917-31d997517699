import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/vip_model_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_progress_bar.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class VipLevelCell extends StatelessWidget {
  final int index;
  final VipModel model;
  final int totalLevel;
  final bool isCurrentLevel;

  VipLevelCell({
    super.key,
    required this.index,
    required this.model,
    required this.totalLevel,
    this.isCurrentLevel = false,
  });

  @override
  Widget build(BuildContext context) {
    final currentIntegral = sl<UserCubit>().state.vipInfo?.integral ?? 0;
    final requiredIntegral = model.growthIntegral;
    final remainingGrowth = (requiredIntegral - currentIntegral) / 1000;
    final vipLevelDes = vipLevelDescriptions[max(index, 0)];

    final growthToNextLevelStr = remainingGrowth > 0
        ? '${'need'.tr()} ${StringUtil().formatLargeNumber('$remainingGrowth')} ${'growth_value_promoted'.tr()}'
        : vipLevelDes;

    final nextLevelTips = index < totalLevel ? growthToNextLevelStr : "stand_out".tr();
    return Container(
      width: double.infinity,
      height: 200.gh,
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF292011),
            const Color(0xFF101010).withOpacity(0.7),
            const Color(0xFF101010),
            const Color(0xFF292011),
          ],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 20.gw),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 10.gw),
                AneText(
                  "VIP ${model.vipLevel}",
                  style: context.textTheme.primary.fs28.w700,
                ),
                SizedBox(
                  width: 0.35.gsw,
                  height: 40.gw,
                  child: AneText(
                    isCurrentLevel ? "current_vip_level".tr() : nextLevelTips,
                    style: context.textTheme.regular.fs12.w500,
                    maxLines: 2,
                  ),
                ),
                SizedBox(height: 40.gw),
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'progress_to'.tr(),
                        style: context.textTheme.title.w600.ffAne,
                      ),
                      TextSpan(
                        text: " $requiredIntegral",
                        style: context.textTheme.primary.fs12.w700.ffAne,
                      ),
                    ],
                  ),
                ),
                const CommonProgressBar(total: 10, current: 8),
                if (remainingGrowth > 0) ...[
                  SizedBox(height: 4.gw),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: 'need'.tr(),
                          style: context.textTheme.title.w600.ffAne,
                        ),
                        TextSpan(
                          text: " ${StringUtil().formatLargeNumber('$remainingGrowth')}K ",
                          style: context.textTheme.primary.ffAne,
                        ),
                        TextSpan(
                          text: 'to_upgrade'.tr(),
                          style: context.textTheme.title.w600.ffAne,
                        ),
                      ],
                    ),
                  ),
                ]
              ],
            ),
          ),
          Positioned(
            right: 0,
            top: 0,
            child: AppImage(imageUrl: 'assets/images/mine/vip/vip_icon.png'),
          )
        ],
      ),
    );
  }

  final List<String> vipLevelDescriptions = [
    // 1 - 10 入门阶段
    '牛刀小试', '初窥门径', '小试锋芒', '渐入佳境', '略有小成',
    '初露锋芒', '勤修苦练', '小有所成', '意气风发', '修行有望',

    // 11 - 20 进阶阶段
    '驰骋江湖', '初成一派', '名声渐起', '挥斥方遒', '气势如虹',
    '技压群雄', '威震四方', '身法轻灵', '心法渐通', '声名鹊起',

    // 21 - 30 成名阶段
    '名动一方', '实力雄厚', '独当一面', '拔尖高手', '内力深厚',
    '纵横四海', '深不可测', '战力惊人', '出神入化', '炉火纯青',

    // 31 - 40 大师阶段
    '技冠群雄', '剑气如虹', '真传弟子', '远近闻名', '暗藏杀机',
    '破茧成蝶', '锋芒毕露', '功力大增', '气定神闲', '初窥大道',

    // 41 - 50 宗师阶段
    '风华正茂', '稳如泰山', '举重若轻', '气场全开', '声震八方',
    '一代高手', '惊才绝艳', '出类拔萃', '天赋异禀', '登堂入室',

    // 51 - 60 王者阶段
    '霸者无双', '所向披靡', '统御四方', '纵横天下', '所向无敌',
    '不世之才', '登峰造极', '破碎虚空', '万法归宗', '唯我独尊',

    // 61 - 70 超凡阶段
    '涅槃重生', '神功大成', '威震江湖', '神出鬼没', '剑随心动',
    '天人合一', '万象归心', '如入无人之境', '强者之路', '问鼎江湖',

    // 71 - 80 绝顶阶段
    '深藏不露', '超然物外', '力压群雄', '所向披靡', '撼天动地',
    '乾坤独断', '呼风唤雨', '神挡杀神', '独孤求败', '不败战神',

    // 81 - 90 传奇阶段
    '传说再临', '武道巅峰', '无懈可击', '举世无双', '天地同寿',
    '剑指苍穹', '封神在即', '九天揽月', '万古流芳', '傲视天地',

    // 91 - 100 神话阶段
    '踏破星河', '无上尊者', '圣体无瑕', '威临万界', '震古烁今',
    '神之血统', '世间独尊', '永恒传说', '极境升华', '万道归一'
  ];
}

class VipCardColorConfig {
  final List<Color> bgColorList;

  const VipCardColorConfig({required this.bgColorList});
}

class VipCardColorManager {
  static VipCardColorConfig getByLevel(int vipLevel) {
    switch (vipLevel) {
      case 1:
        return vip1;
      case 2:
        return vip2;
      case 3:
        return vip3;
      case 4:
        return vip4;
      case 5:
        return vip5;
      case 6:
        return vip6;
      case 7:
        return vip7;
      case 8:
        return vip8;
      case 9:
        return vip9;
      case 10:
        return vip10;
      default:
        return vip1;
    }
  }

  static const stops = [
    0.0,
    0.55,
    0.81,
    0.88,
    1.0,
  ];

  static const vip1 = VipCardColorConfig(bgColorList: [
    Color(0xFFDBCFC9),
    Color(0xFFDBCFC9),
    Color(0xFFF7F9FB),
    Color(0xFFDBCFC9),
    Color(0xFFF8EAE3),
  ]);

  static const vip2 = VipCardColorConfig(bgColorList: [
    Color(0xFFE1CBCB),
    Color(0xFFE8D2D2),
    Color(0xFFFFF2F2),
    Color(0xFFE2CCCC),
    Color(0xFFFBE5E5),
  ]);

  static const vip3 = VipCardColorConfig(bgColorList: [
    Color(0xFFE0CAD5),
    Color(0xFFE0CAD5),
    Color(0xFFFFF0F7),
    Color(0xFFE0CAD5),
    Color(0xFFFBE5F0),
  ]);

  static const vip4 = VipCardColorConfig(bgColorList: [
    Color(0xFFD5CDE3),
    Color(0xFFD5CDE3),
    Color(0xFFF9F5FF),
    Color(0xFFD6CEE4),
    Color(0xFFEFE7FD),
  ]);

  static const vip5 = VipCardColorConfig(bgColorList: [
    Color(0xFFDDCAE0),
    Color(0xFFE5D2E8),
    Color(0xFFFEF7FF),
    Color(0xFFDECBE1),
    Color(0xFFF7E4FA),
  ]);

  static const vip6 = VipCardColorConfig(bgColorList: [
    Color(0xFFCED0E4),
    Color(0xFFCED0E4),
    Color(0xFFF3F4FF),
    Color(0xFFD0D2E6),
    Color(0xFFE9EBFF),
  ]);

  static const vip7 = VipCardColorConfig(bgColorList: [
    Color(0xFFCBD2E1),
    Color(0xFFD9E0EF),
    Color(0xFFF6F9FF),
    Color(0xFFCBD2E1),
    Color(0xFFE5ECFB),
  ]);

  static const vip8 = VipCardColorConfig(bgColorList: [
    Color(0xFFC7D6DD),
    Color(0xFFC7D6DD),
    Color(0xFFEDF9FF),
    Color(0xFFC7D6DD),
    Color(0xFFE0EFF6),
  ]);

  static const vip9 = VipCardColorConfig(bgColorList: [
    Color(0xFFD2D2D2),
    Color(0xFFD5D5D5),
    Color(0xFFF7F9FB),
    Color(0xFFD3D3D3),
    Color(0xFFEBEBEB),
  ]);

  static const vip10 = VipCardColorConfig(bgColorList: [
    Color(0xFFE0D0B9),
    Color(0xFFE8D8C1),
    Color(0xFFFFF5E7),
    Color(0xFFE0D0B9),
    Color(0xFFFAEAD3),
  ]);
}
