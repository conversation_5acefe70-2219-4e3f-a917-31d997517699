import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path/path.dart';
import 'package:wd/core/constants/base64_image.dart';
import 'package:wd/core/models/entities/home_feed_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/hot_push_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class WelcomeBonusDialog {
  final List<HomeFeedItem> data;

  WelcomeBonusDialog({
    required this.data,
  });

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _WelcomeBonusDialogContent(
          data: data,
        );
      },
    );
  }
}

class _WelcomeBonusDialogContent extends StatelessWidget {
  final List<HomeFeedItem> data;

  const _WelcomeBonusDialogContent({
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SizedBox(width: 362.gw,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildCloseButton(context),
              SizedBox(height: 12.gw),
              Container(
                padding: EdgeInsets.all(24.gw),
                decoration: BoxDecoration(
                  image: const DecorationImage(image: AssetImage("assets/images/alert/bg_common.png"), fit: BoxFit.fill),
                  borderRadius: BorderRadius.circular(16.gw),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTitle(context),
                    SizedBox(height: 24.gw),
                    _buildListViewWidget(),
                    SizedBox(height: 16.gw),
                    _buildLoginTips(context),
                    SizedBox(height: 24.gw),
                    CommonButton(
                      title: 'choose'.tr(),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          width: 32.gw,
          height: 32.gw,
          decoration: BoxDecoration(
            color: context.colorTheme.textSecondary.withOpacity(0.1),
            border: Border.all(color: context.colorTheme.textTitle),
            borderRadius: BorderRadius.circular(16.gw),
          ),
          child: Icon(
            Icons.close,
            color: context.colorTheme.textSecondary,
            size: 16.gw,
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          'choose_your'.tr(), // 选择你的
          style: context.textTheme.secondary.fs24.w600,
        ),
        SizedBox(width: 5.gw),
        AneText(
          "welcome_bonus".tr(), // 欢迎礼包
          style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
        ),
      ],
    );
  }

  _buildListViewWidget() {
    return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          if (index == data.length) {
            return _buildListItem(
              context,
              imgUrl: "assets/images/activity/icon_welcome_dont_want.png",
              content: "no_welcome_bonus".tr(),
              isSel: false,
              onTap: () {},
            );
          }
          final model = data[index];
          return _buildListItem(context,
              imgUrl: model.noticeContent,
              title: model.noticeTitle,
              content: model.description,
              isSel: true,
              onTap: () {});
        },
        separatorBuilder: (_, __) => SizedBox(height: 8.gw),
        itemCount: data.length + 1);
  }

  Widget _buildListItem(
    BuildContext context, {
    required String imgUrl,
    String? title,
    String? content,
    required bool isSel,
    required GestureTapCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 10.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.foregroundColor,
          borderRadius: BorderRadius.circular(8.gw),
        ),
        child: Row(
          children: [
            AppImage(imageUrl: imgUrl, width: 53.gw, height: 53.gw),
            SizedBox(width: 16.gw),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title != null) ...[
                    AneText(
                      title,
                      style: context.textTheme.secondary.fs16.w600,
                    ),
                  ],
                  if (title != null && content != null) ...[
                    SizedBox(
                      height: 5.gw,
                    )
                  ],
                  if (content != null) ...[
                    AneText(
                      content,
                      style: context.textTheme.secondary.ffAne.opa80,
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(width: 10.gw),
            SvgPicture.asset(
              isSel
                  ? "assets/images/checkmark/icon_checkmark_spot_selected.svg"
                  : "assets/images/checkmark/icon_checkmark_spot_unselected.svg",
              width: 14.gw,
              height: 14.gw,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginTips(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "already_have_account".tr(),
          style: context.textTheme.title.fs16.ffAne,
        ),
        SizedBox(width: 3.gw),
        Text("sign_in".tr(), style: context.textTheme.primary.fs16.ffAne),
      ],
    );
  }
}
