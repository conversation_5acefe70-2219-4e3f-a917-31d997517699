import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/video_filter_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';

class HomeVideoLibraryFilterView extends StatefulWidget {
  final List<VideoFilterEntity> dataList;
  final VideoFilterEntity current;
  final ValueChanged<VideoFilterEntity> onFilterSelected; // 回调函数

  const HomeVideoLibraryFilterView({
    super.key,
    required this.dataList,
    required this.onFilterSelected,
    required this.current,
  });

  @override
  State createState() => _HomeVideoLibraryFilterViewState();
}

class _HomeVideoLibraryFilterViewState extends State<HomeVideoLibraryFilterView> {
  bool isExpanded = false; // 控制展开/折叠
  int currentSelIndex = 0;

  void _updateCurrentSelIndex(int index) {
    if (currentSelIndex != index) {
      setState(() {
        currentSelIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isExpanded)
          Expanded(
            child: CommonTabBar.withAutoKey(
              widget.dataList.map((e) => CommonTabBarItem(title: e.videoCategory)).toList(),
              style: CommonTabBarStyle.secondary,
              showEndMark: true,
              currentIndex: currentSelIndex,
              onTap: (index) {
                widget.onFilterSelected(widget.dataList[index]);
                _updateCurrentSelIndex(index);
              },
            ),
          ),
        if (isExpanded)
          Expanded(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300), // 动画过渡时间
              child: Container(
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.circular(12.gw),
                ),
                padding: EdgeInsets.symmetric(horizontal: 7.gw, vertical: 4.gw),
                child: Wrap(
                  spacing: 4.gw, // 元素之间的水平间距
                  runSpacing: 8.gw, // 元素之间的垂直间距
                  children: widget.dataList.map((model) {
                    return CategoryItem(
                      model: model,
                      isSelected: model.videoCategoryId == widget.current.videoCategoryId,
                      onTap: () {
                        widget.onFilterSelected(model);
                        _updateCurrentSelIndex(widget.dataList.indexOf(model));
                      },
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        SizedBox(width: 8.gw),
        InkWell(
          onTap: () {
            setState(() {
              isExpanded = !isExpanded; // 切换展开状态
            });
          },
          child: Container(
              width: 37.gw,
              height: 36.gw,
              alignment: Alignment.centerRight,
              child: Image.asset(
                "assets/images/tiktok/btn_video_filter.png",
                width: 37.gw,
                height: 36.gw,
              )),
        )
      ],
    );
  }
}

class CategoryItem extends StatelessWidget {
  final VideoFilterEntity model;
  final bool isSelected;
  final GestureTapCallback? onTap;

  const CategoryItem({
    super.key,
    required this.model,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: IntrinsicWidth(
        child: Container(
          height: 32.gw,
          padding: EdgeInsets.symmetric(horizontal: isSelected ? 12.gw : 11.gw),
          // padding: 4px 6px 4px 6px;
          decoration: BoxDecoration(
            color: isSelected ? context.colorTheme.tabItemBgA : context.colorTheme.borderA,
            border: isSelected ? Border.all(color: context.colorTheme.borderE, width: 1) : null,
            borderRadius: BorderRadius.all(Radius.circular(10.gw)),
          ),
          alignment: Alignment.center,
          child: Text(
            model.videoCategory,
            style: context.textTheme.title.copyWith(color: isSelected ? context.theme.primaryColor : null),
          ),
        ),
      ),
    );
  }
}
