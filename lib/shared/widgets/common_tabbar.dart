import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

enum CommonTabBarStyle {
  primary,
  secondary,
}

class CommonTabBarItem {
  final String title;
  final String? imageUrl;
  final Color? imageActiveColor;
  final Color? imageInactiveColor;

  CommonTabBarItem({
    required this.title,
    this.imageUrl,
    this.imageActiveColor,
    this.imageInactiveColor,
  });
}

class CommonTabBar extends StatefulWidget {
  final List<CommonTabBarItem> data;
  final CommonTabBarStyle? style;
  final ValueChanged<int>? onTap;
  final int currentIndex;
  final bool showEndMark;
  final bool isScrollable;

  const CommonTabBar(
    this.data, {
    required super.key,
    this.style = CommonTabBarStyle.primary,
    this.onTap,
    this.currentIndex = 0,
    this.isScrollable = true,
    this.showEndMark = false,
  });

  factory CommonTabBar.withAutoKey(
      List<CommonTabBarItem> data, {
        CommonTabBarStyle style = CommonTabBarStyle.primary,
        ValueChanged<int>? onTap,
        int currentIndex = 0,
        bool isScrollable = true,
        bool showEndMark = false,
      }) {
    return CommonTabBar(
      data,
      key: ValueKey(data.length),
      style: style,
      onTap: onTap,
      currentIndex: currentIndex,
      isScrollable: isScrollable,
      showEndMark: showEndMark,
    );
  }

  @override
  State<StatefulWidget> createState() => _CommonTabBarState();
}

class _CommonTabBarState extends State<CommonTabBar> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late int _currentIndex = widget.currentIndex;
  late final _height = 40.gw;
  late final _tabBarHeight = 32.gw;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.data.length,
      vsync: this,
      initialIndex: widget.currentIndex,
    );
    _tabController.addListener(_handleTabChange);
  }

  @override
  void didUpdateWidget(covariant CommonTabBar old) {
    super.didUpdateWidget(old);

    // 1. 如果 data 长度变化，重新创建 controller
    if (old.data.length != widget.data.length) {
      _tabController
        ..removeListener(_handleTabChange)
        ..dispose();

      _tabController = TabController(
        length: widget.data.length,
        vsync: this,
        initialIndex: widget.currentIndex.clamp(0, widget.data.length - 1),
      )..addListener(_handleTabChange);

    }

    // 2. 如果外部 currentIndex 变化，同步到 TabController
    if (_currentIndex != widget.currentIndex) {
      _tabController.animateTo(widget.currentIndex);
    }
  }

  void _handleTabChange() {
    // 只有用户手指拖动/点击才触发，避免外部 animateTo 再反吹
    if (_tabController.indexIsChanging) {
      _currentIndex = _tabController.index;
      widget.onTap?.call(_tabController.index);
      setState(() {});
    }
  }

  @override
  void dispose() {
    _tabController
      ..removeListener(_handleTabChange)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: _height,
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(12.gw),
          ),
          padding: EdgeInsets.symmetric(horizontal: 4.gw),
          alignment: Alignment.center,
          child: SizedBox(
            height: _tabBarHeight,
            child: TabBar(
                padding: EdgeInsets.zero,
                isScrollable: widget.isScrollable,
                tabAlignment: widget.isScrollable ? TabAlignment.start : null,
                controller: _tabController,
                splashFactory: NoSplash.splashFactory,
                overlayColor: WidgetStateProperty.all(context.theme.cardColor),
                labelPadding: EdgeInsets.symmetric(horizontal: 3.gw),
                labelStyle: context.textTheme.primary,
                labelColor: context.theme.primaryColor,
                unselectedLabelStyle: context.textTheme.title,
                unselectedLabelColor: context.colorTheme.textTitle,
                indicator: BoxDecoration(
                  color: context.colorTheme.tabItemBgA,
                  borderRadius: BorderRadius.circular(10.gw),
                  border: Border.all(color: context.colorTheme.borderE, width: 1),
                ),
                indicatorWeight: 0,
                indicatorSize: TabBarIndicatorSize.tab,
                tabs: [
                  for (int i = 0; i < widget.data.length; i++)
                    _buildTabItem(
                      widget.data[i],
                      isSel: _tabController.index == i,
                    )
                ]),
          ),
        ),

        /// 右侧阴影蒙层
        if (widget.showEndMark && _tabController.index != widget.data.length - 1)
          Positioned(
            top: 0,
            right: 0,
            child: IgnorePointer(
              child: Container(
                height: _height,
                width: 110.gw,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.gw),
                  gradient: LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      context.theme.cardColor,
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTabItem(CommonTabBarItem item, {bool isSel = false}) {
    bool showBgDecoration = widget.style == CommonTabBarStyle.secondary && !isSel;
    final imageUrl = item.imageUrl;
    return Tab(
        child: Container(
      height: _tabBarHeight,
      padding: EdgeInsets.symmetric(horizontal: 11.gw),
      decoration: showBgDecoration
          ? BoxDecoration(
              color: context.colorTheme.borderA,
              borderRadius: BorderRadius.circular(10.gw),
            )
          : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (imageUrl != null) ...[
            imageUrl.endsWith("svg")
                ? _buildSvgPicture(
                    imageUrl,
                    isSel,
                    imageActiveColor: item.imageActiveColor,
                    imageInactiveColor: item.imageInactiveColor,
                  )
                : AppImage(
                    imageUrl: imageUrl,
                    height: 16.gw,
                  ),
            SizedBox(width: 6.gw),
          ],
          Text(item.title),
        ],
      ),
    ));
  }

  Widget _buildSvgPicture(imageUrl, isSel, {Color? imageActiveColor, Color? imageInactiveColor}) {
    final haveImageColor = imageActiveColor != null && imageInactiveColor != null;
    return SvgPicture.asset(
      imageUrl,
      height: 16.gw,
      colorFilter:
          haveImageColor ? ColorFilter.mode(isSel ? imageActiveColor : imageInactiveColor, BlendMode.srcIn) : null,
    );
  }
}
