import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class CommonBottomSheet extends StatelessWidget {
  final String? title;
  final List<Widget> children;
  final bool isScrollable;
  final double? maxHeight;
  final double? minHeight;
  final Color? backgroundColor;
  final double borderRadius;
  final EdgeInsets? contentPadding;
  final bool showGradientOverlay;
  final double gradientOverlayHeight;
  final List<Color>? gradientOverlayColors;
  final List<double>? gradientOverlayStops;
  final bool isDismissible;
  final VoidCallback? onTapSure;
  final VoidCallback? onTapCancel;

  const CommonBottomSheet({
    super.key,
    this.title,
    required this.children,
    this.isScrollable = true,
    this.maxHeight,
    this.minHeight,
    this.backgroundColor,
    this.borderRadius = 16.0,
    this.contentPadding,
    this.showGradientOverlay = true,
    this.gradientOverlayHeight = 30.0,
    this.gradientOverlayColors,
    this.gradientOverlayStops,
    this.isDismissible = true,
    this.onTapSure,
    this.onTapCancel,
  });

  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    required List<Widget> children,
    bool isScrollable = true,
    double? maxHeight,
    double? minHeight,
    Color? backgroundColor,
    double borderRadius = 16.0,
    EdgeInsets? contentPadding,
    bool showGradientOverlay = false,
    double gradientOverlayHeight = 60.0,
    List<Color>? gradientOverlayColors,
    List<double>? gradientOverlayStops,
    bool isDismissible = true,
    bool enableDrag = true,
    bool isScrollControlled = true,
    Color? barrierColor,
    VoidCallback? onTapSure,
    VoidCallback? onTapCancel,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      isScrollControlled: isScrollControlled,
      barrierColor: barrierColor,
      builder: (BuildContext context) {
        return CommonBottomSheet(
          title: title,
          isScrollable: isScrollable,
          maxHeight: maxHeight,
          minHeight: minHeight,
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          contentPadding: contentPadding,
          showGradientOverlay: showGradientOverlay,
          gradientOverlayHeight: gradientOverlayHeight,
          gradientOverlayColors: gradientOverlayColors,
          gradientOverlayStops: gradientOverlayStops,
          isDismissible: isDismissible,
          onTapSure: onTapSure,
          onTapCancel: onTapCancel,
          children: children,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final defaultMaxHeight = screenHeight * 0.9;
    final effectiveMaxHeight = maxHeight ?? defaultMaxHeight;
    final effectiveMinHeight = minHeight ?? 200.gw;

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: isDismissible
          ? () {
              // Allow tapping outside to dismiss only if isDismissible is true
              Navigator.of(context).pop();
            }
          : null,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: 20.gw,
              right: 20.gw,
              bottom: 50.gw,
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: effectiveMaxHeight,
                minHeight: effectiveMinHeight,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: backgroundColor ?? context.theme.cardColor,
                  borderRadius: BorderRadius.circular(borderRadius.gw),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (title != null) _buildHeader(context),
                    _buildContent(context),
                    _buildBottomButtons(context),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the header section
  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 61.gw,
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(borderRadius.gw),
          topRight: Radius.circular(borderRadius.gw),
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.gw),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (title != null)
                  AneText(
                    title!,
                    style: context.textTheme.secondary.fs20.w500,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the content section
  Widget _buildContent(BuildContext context) {
    if (isScrollable) {
      if (showGradientOverlay) {
        return Expanded(
          child: ShaderMask(
            shaderCallback: (Rect bounds) {
              return LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white, // Content fully visible at top
                  Colors.white, // Content fully visible
                  const Color(0xFF101010).withOpacity(1.0), // 100% #101010
                  const Color(0xFF101010).withOpacity(0.8), // 80% #101010
                  const Color(0xFF101010).withOpacity(0.4788), // 47.88% #101010
                  const Color(0xFF101010)
                      .withOpacity(0.0), // 0% #101010 (transparent)
                ],
                stops: const [0.0, 0.6, 0.7, 0.8, 0.9, 1.0],
              ).createShader(bounds);
            },
            blendMode: BlendMode.dstIn,
            child: GestureDetector(
              behavior: HitTestBehavior.deferToChild,
              child: SingleChildScrollView(
                padding: contentPadding ?? EdgeInsets.all(16.gw),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: children,
                ),
              ),
            ),
          ),
        );
      } else {
        return Flexible(
          child: SingleChildScrollView(
            child: Padding(
              padding: contentPadding ?? EdgeInsets.all(16.gw),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: children,
              ),
            ),
          ),
        );
      }
    }

    return Padding(
      padding: contentPadding ?? EdgeInsets.all(16.gw),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  /// Builds the bottom buttons section
  Widget _buildBottomButtons(BuildContext context) => onTapSure != null
      ? Container(
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(borderRadius.gw),
              bottomRight: Radius.circular(borderRadius.gw),
            ),
          ),
          padding: EdgeInsets.all(16.gw),
          child: Row(
            children: _buildButtonList(context),
          ),
        )
      : const SizedBox.shrink();

  List<Widget> _buildButtonList(BuildContext context) => [
        Expanded(
          child: CommonButton(
            title: 'cancel'.tr(),
            height: 47.gw,
            style: CommonButtonStyle.secondary,
            backgroundColor: context.colorTheme.foregroundColor,
            borderColor: context.colorTheme.borderA,
            onPressed: () {
              onTapCancel?.call();
              Navigator.pop(context);
            },
          ),
        ),
        SizedBox(width: 16.gw),
        Expanded(
          child: CommonButton(
            title: 'ok'.tr(),
            height: 47.gw,
            style: CommonButtonStyle.primary,
            backgroundColor: context.colorTheme.btnBgPrimary,
            textColor: context.colorTheme.btnTitlePrimary,
            borderColor: context.colorTheme.btnBorderPrimary,
            onPressed: () {
              onTapSure?.call();
              Navigator.pop(context);
            },
          ),
        ),
      ];
}
