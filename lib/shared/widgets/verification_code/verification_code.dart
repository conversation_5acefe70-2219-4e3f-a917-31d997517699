import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../../../core/constants/constants.dart';
import '../../../core/models/apis/user.dart';
import '../../../features/page/login/login_state.dart';
import '../../../shared/widgets/wangyi_captcha/wangyi_captcha.dart';
import '../common_button.dart';

class VerificationCode extends StatefulWidget {
  final String? text;
  final Function()? onPressed;
  final bool isGradient;
  final int countdown;
  final String phone;
  final String? email;
  final CaptchaType captchaType;
  final bool isDebug;
  final bool checkIsBind;
  final Function(String smsCode)? onSmsCode;
  final Function(String emailCode)? onEmailCode;
  final int? areaCode;
  final bool isEmailMode;

  const VerificationCode({
    super.key,
    this.text,
    this.onPressed,
    this.isGradient = false,
    this.countdown = 60,
    required this.phone,
    this.email,
    this.captchaType = CaptchaType.wangYi,
    this.isDebug = false,
    this.checkIsBind = true,
    this.onSmsCode,
    this.onEmailCode,
    this.areaCode,
    this.isEmailMode = false,
  });

  @override
  State<VerificationCode> createState() => _VerificationCodeState();
}

class _VerificationCodeState extends State<VerificationCode> {
  int _countdown = 0;

  @override
  void initState() {
    super.initState();
    _countdown = 0;
  }

  void _startTimer() {
    setState(() => _countdown = widget.countdown);

    Future.doWhile(() async {
      if (_countdown <= 0 || !mounted) return false;

      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        setState(() => _countdown--);
      }

      return _countdown > 0;
    });
  }

  void _showWangYiCaptcha({
    required String account,
    required Function(String result) onSuccess,
  }) {
    WangYiCaptcha().show(
      account: account,
      captchaId: kWangYiVerityKey,
      onSuccess: onSuccess,
      onValidateFailClose: () {},
      onError: () {
        GSEasyLoading.showToast('verify_failed'.tr());
      },
    );
  }

  Future<void> _handlePress() async {
    if (widget.isEmailMode) {
      // Email validation
      if (widget.email?.isEmpty ?? true) {
        GSEasyLoading.showToast('please_input_email'.tr());
        return;
      }
      // Validate email format
      final bool isValidEmail = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(widget.email!);
      if (!isValidEmail) {
        GSEasyLoading.showToast('please_input_correct_email_format'.tr());
        return;
      }
      _handleSendEmailCode();
    } else {
      // Phone validation
      if (widget.phone.isEmpty) {
        GSEasyLoading.showToast('please_input_phone_number'.tr());
        return;
      }
      // // Validate phone number length and format
      // final bool isValidLength = widget.phone.length == 11;
      // final bool isValidFormat = RegExp(r'^1[3-9]\d{9}$').hasMatch(widget.phone);

      // if (!isValidLength || !isValidFormat) {
      //   GSEasyLoading.showToast('请输入正确的手机号');
      //   return;
      // }
      if (widget.checkIsBind) {
        bool isBind = await _handleCheckPhoneIsBindUser();
        if (isBind) {
          GSEasyLoading.showToast('phone_number_already_bound'.tr());
          return;
        }
      }
      if (widget.captchaType == CaptchaType.wangYi && !widget.isDebug) {
        _showWangYiCaptcha(
          account: widget.phone,
          onSuccess: (result) async {
            widget.onPressed?.call();
            _handleSendSmsCode();
          },
        );
      } else {
        _handleSendSmsCode();
      }
    }
  }

  Future<void> _handleSendSmsCode() async {
    GSEasyLoading.showLoading();
    final result = await UserApi.doSendSmsCode(
      phoneNo: widget.phone,
      areaCode: widget.areaCode,
    );
    if (result) {
      GSEasyLoading.showToast('sms_code_sent_successfully'.tr());
      _startTimer();
      if (kDebug) _handleTestUseGetSmsCode();
    } else {
      setState(() => _countdown = 0);
    }
    GSEasyLoading.dismiss();
  }

  Future<void> _handleSendEmailCode() async {
    GSEasyLoading.showLoading();
    final result = await UserApi.doSendMailCode(
      mail: widget.email!,
    );
    if (result) {
      GSEasyLoading.showToast('email_code_sent_successfully'.tr());
      _startTimer();
      if (kDebug) _handleTestUseGetEmailCode();
    } else {
      setState(() => _countdown = 0);
    }
    GSEasyLoading.dismiss();
  }

  Future<void> _handleTestUseGetSmsCode() async {
    final result = await UserApi.testUseGetSmsCode(widget.phone);
    if (result.isNotEmpty) widget.onSmsCode?.call(result);
  }

  Future<void> _handleTestUseGetEmailCode() async {
    final result = await UserApi.testUseGetMailCode(widget.email!);
    if (result.isNotEmpty) widget.onEmailCode?.call(result);
  }

  Future<bool> _handleCheckPhoneIsBindUser() async {
    final result = await UserApi.checkPhoneIsBindUser(widget.phone);
    if (result) {
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = _countdown <= 0;

    return widget.isGradient
        ? GestureDetector(
            onTap: isEnabled ? _handlePress : null,
            child: Container(
              height: 50,
              width: 103,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(8.0),
                  bottomRight: Radius.circular(8.0),
                ),
                gradient: LinearGradient(
                  colors: [Color(0xffEACA9F), Color(0xffB9936D)],
                ),
              ),
              child: Center(
                child: Text(
                  _countdown > 0 ? '${_countdown}s' : widget.text ?? 'get_code'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          )
        : SizedBox(
            width: 72.gw,
            height: 33.gw,
            child: CommonButton(
              onPressed: isEnabled ? _handlePress : null,
              title: _countdown > 0 ? '${_countdown}s' : widget.text ?? 'get_code'.tr(),
              style: CommonButtonStyle.quaternary,
              backgroundColor: context.colorTheme.tabItemBgA,
              borderColor: context.colorTheme.tabItemBgA,
              textStyle: context.textTheme.secondary.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
          );
  }
}
