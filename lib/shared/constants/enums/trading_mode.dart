import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';

/// 交易模式配置
enum TradingMode {
  stock, // 股票
  futures, // 期货
  stockAndFutures, // 股票&期货
}

extension TradingModeExtension on TradingMode {
  int get customIndex {
    switch (this) {
      case TradingMode.stock:
        return 1;
      case TradingMode.futures:
        return 2;
      case TradingMode.stockAndFutures:
        return 3;
    }
  }

  static TradingMode fromIndex(int index) {
    if (index <= 0 || index >= 4) {
      return TradingMode.stockAndFutures;
    }
    if (index == 1) {
      return TradingMode.stock;
    }
    if (index == 2) {
      return TradingMode.futures;
    }
    if (index == 3) {
      return TradingMode.stockAndFutures;
    }
    return TradingMode.stockAndFutures;
  }

  List<MarketCategory> get allCategories {
    switch (this) {
      case TradingMode.stock:
        return [
          MarketCategory.cnStocks,
          MarketCategory.hkStocks,
          MarketCategory.usStocks,
          MarketCategory.stockIndex,
        ];
      case TradingMode.futures:
        return [
          MarketCategory.cnFutures,
        ];
      case TradingMode.stockAndFutures:
        return [
          MarketCategory.cnStocks,
          MarketCategory.hkStocks,
          MarketCategory.usStocks,
          MarketCategory.stockIndex,
          MarketCategory.cnFutures,
        ];
    }
  }
}
