import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/sys_config_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/sys_config_model.g.dart';

@JsonSerializable()
class SysConfigModel {
  /// (1:股票, 2:期货, 3:股票+期货)
  int tradingMode = 0;

  SysConfigModel();

  factory SysConfigModel.fromJson(Map<String, dynamic> json) => $SysConfigModelFromJson(json);

  Map<String, dynamic> toJson() => $SysConfigModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
